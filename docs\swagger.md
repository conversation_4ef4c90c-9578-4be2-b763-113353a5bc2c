# Supabase Functions API Documentation

This document provides Swagger-like documentation for the Supabase Edge Functions used in the Fleet Management Dashboard. For full OpenAPI specification, use the Supabase dashboard or tools like Swagger Editor.

## Authentication
All endpoints require JWT authentication via Supabase Auth. Include the `Authorization: Bearer <token>` header.

### Base URL
```
https://<project-ref>.supabase.co/functions/v1/
```

## 1. Vehicle Management Functions

### GET /get-vehicles-with-latest-location
**Description**: Retrieves vehicles with their latest location data, joining locations table.

**Parameters**:
- `limit` (query, optional): Number of vehicles to return (default: 50)
- `branch_id` (query, optional): Filter by branch ID

**Responses**:
- `200`: Array of vehicle objects with location data
  ```json
  [
    {
      "vehicle_id": "uuid",
      "plate_number": "ABC-123",
      "lat": 24.7136,
      "lng": 46.6753,
      "speed_kph": 45,
      "timestamp": "2025-01-07T10:30:00Z",
      "location_name": "King Fahd Road, Riyadh"
    }
  ]
  ```
- `401`: Unauthorized
- `500`: Internal server error

### POST /create-vehicle-maintenance-reminder
**Description**: Creates a maintenance reminder for a vehicle.

**Body**:
```json
{
  "vehicle_id": "uuid",
  "service_type": "preventive",
  "due_km": 50000,
  "due_date": "2025-06-01"
}
```

**Responses**:
- `201`: Created reminder
  ```json
  {
    "id": "uuid",
    "vehicle_id": "uuid",
    "service_type": "preventive",
    "due_km": 50000,
    "due_date": "2025-06-01T00:00:00Z",
    "created_at": "2025-01-07T10:30:00Z"
  }
  ```
- `400`: Invalid request data
- `401`: Unauthorized

## 2. Driver Management Functions

### GET /get-driver-performance-score
**Description**: Calculates and returns a driver's performance score based on violations, fuel efficiency, and maintenance records.

**Parameters**:
- `driver_id` (query, required): ID of the driver

**Responses**:
- `200`: Driver performance data
  ```json
  {
    "driver_id": "uuid",
    "performance_score": 8.5,
    "violations_count": 2,
    "fuel_efficiency": 12.3,
    "last_assessment": "2025-01-07T10:30:00Z"
  }
  ```
- `404`: Driver not found
- `500`: Internal server error

### POST /update-driver-license
**Description**: Updates driver's license information and image.

**Body**:
```json
{
  "driver_id": "uuid",
  "license_number": "123456789",
  "expiry_date": "2026-01-01",
  "license_image_url": "https://storage...",
  "storage_type": "url"
}
```

**Responses**:
- `200`: Updated driver data
- `400`: Invalid license data
- `401`: Unauthorized

## 3. Alert Management Functions

### POST /create-vehicle-alert
**Description**: Creates a new vehicle alert (e.g., low fuel, overdue maintenance).

**Body**:
```json
{
  "vehicle_id": "uuid",
  "alert_type": "low_fuel",
  "severity": "medium",
  "message": "Fuel level below 25%",
  "metadata": {}
}
```

**Responses**:
- `201`: Created alert
  ```json
  {
    "id": "uuid",
    "vehicle_id": "uuid",
    "alert_type": "low_fuel",
    "severity": "medium",
    "message": "Fuel level below 25%",
    "acknowledged": false,
    "created_at": "2025-01-07T10:30:00Z"
  }
  ```
- `400`: Invalid alert data
- `401`: Unauthorized

## 4. Reporting Functions

### GET /generate-monthly-report
**Description**: Generates a comprehensive monthly fleet report with statistics.

**Parameters**:
- `month` (query, required): Month in YYYY-MM format (e.g., 2025-01)
- `branch_id` (query, optional): Filter by branch

**Responses**:
- `200`: Monthly report data
  ```json
  {
    "month": "2025-01",
    "total_revenue": 150000,
    "total_expenses": 85000,
    "fuel_consumption": 12500,
    "vehicles_active": 45,
    "maintenance_costs": 25000,
    "driver_performance_avg": 8.2
  }
  ```
- `400`: Invalid month format
- `500`: Report generation failed

## 5. Rate Limiting & Security

### Note on Rate Limiting
All functions are protected by database-level rate limiting policies:
- **Vehicles table**: 100 operations per minute per user
- **Users table**: 50 operations per minute per user
- **Storage operations**: 20 uploads/downloads per minute per user

Exceeding limits returns HTTP 429 Too Many Requests.

## Error Handling

### Common Error Responses
- `400 Bad Request`: Invalid input data
  ```json
  {
    "error": "Invalid parameter",
    "details": "Parameter 'vehicle_id' is required"
  }
  ```
- `401 Unauthorized`: Missing or invalid JWT token
  ```json
  {
    "error": "Unauthorized",
    "details": "Invalid authentication token"
  }
  ```
- `403 Forbidden`: User lacks required permissions
  ```json
  {
    "error": "Forbidden",
    "details": "Insufficient permissions for this operation"
  }
  ```
- `429 Too Many Requests`: Rate limit exceeded
  ```json
  {
    "error": "Rate Limit Exceeded",
    "details": "Too many requests. Please wait before trying again."
  }
  ```
- `500 Internal Server Error`: Server-side error
  ```json
  {
    "error": "Internal Server Error",
    "details": "An unexpected error occurred"
  }
  ```

## Usage Examples

### Using curl for Authentication
```bash
# Get JWT token (client-side)
curl -X POST 'https://<project-ref>.supabase.co/auth/v1/token' \
  -H 'Content-Type: application/json' \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'

# Use token in requests
curl -X GET 'https://<project-ref>.supabase.co/functions/v1/get-vehicles-with-latest-location' \
  -H 'Authorization: Bearer <jwt-token>'
```

### Using JavaScript/TypeScript
```typescript
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY)

// Call edge function
const { data, error } = await supabase.functions.invoke('get-vehicles-with-latest-location', {
  body: { limit: 10 }
})

if (error) {
  console.error('Error:', error)
} else {
  console.log('Vehicles:', data)
}
```

## Deployment & Configuration

### Environment Variables
- `SUPABASE_URL`: Your Supabase project URL
- `SUPABASE_ANON_KEY`: Public anon key for client-side operations
- `SUPABASE_SERVICE_ROLE_KEY`: Service role key for admin operations (server-side only)

### RLS Policies
All tables have Row Level Security (RLS) enabled. Common policies:
- `Users can only access their own data`: `auth.uid() = user_id`
- `Admins can access all data`: `auth.role() = 'admin'`
- `Branch managers can access branch data`: `branch_id = (SELECT branch_id FROM users WHERE id = auth.uid())`

### Monitoring
- Use Supabase Logs to monitor function execution
- Track rate limiting via `rate_limit_logs` table
- Monitor database performance with Supabase Analytics

## Future Enhancements

1. **Pagination**: Add offset/limit parameters to list endpoints
2. **Filtering**: Support advanced filtering by status, date ranges, etc.
3. **Webhooks**: Add webhook support for external integrations
4. **Caching**: Implement Redis caching for frequently accessed data
5. **Validation**: Add comprehensive input validation with schemas
6. **Rate Limiting**: Advanced rate limiting with IP-based restrictions

## Contact
For questions or support, contact the development team at [<EMAIL>](mailto:<EMAIL>)

---
*Generated on: 2025-01-07*