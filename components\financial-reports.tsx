"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { DollarSign, TrendingUp, TrendingDown, Pie<PERSON>hart } from "lucide-react"
import { useFuelTrends, useMaintenanceCosts } from "@/hooks/use-fleet-data"
import { LoadingSpinner } from "@/components/loading-spinner"
import { ErrorDisplay } from "@/components/error-display"
import { FuelTrend } from "@/hooks/use-fleet-data"

interface MaintenanceCost {
  month_year: string;
  total_maintenance_cost_egp: number;
  service_count: number;
}

interface FinancialReportsProps {
  language: "ar" | "en"
}

export function FinancialReports({ language }: FinancialReportsProps) {
  const { data: fuelTrends, loading: fuelLoading, error: fuelError } = useFuelTrends(30)
  const { data: maintenanceCostsData, loading: maintenanceLoading, error: maintenanceError } = useMaintenanceCosts(12)

  const maintenanceCosts: MaintenanceCost[] = maintenanceCostsData || []

  if (fuelLoading || maintenanceLoading) {
    return <LoadingSpinner className="h-64" />
  }

  if (fuelError || maintenanceError) {
    return <ErrorDisplay error={fuelError || maintenanceError || "Unknown error"} />
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(language === "ar" ? "ar-EG" : "en-EG", {
      style: "currency",
      currency: "EGP",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  // Calculate totals
  const totalFuelCost = (fuelTrends as FuelTrend[]).reduce((sum, entry) => sum + (entry.total_cost_egp || 0), 0)
  const totalMaintenanceCost = maintenanceCosts.reduce((sum, entry: MaintenanceCost) => sum + (entry.total_maintenance_cost_egp || 0), 0)
  const totalOperatingCost = totalFuelCost + totalMaintenanceCost

  // Mock additional financial data
  const mockFinancialData = {
    insurance_cost: 25000,
    depreciation: 45000,
    driver_salaries: 180000,
    other_expenses: 15000,
  }

  const totalCosts =
    totalOperatingCost +
    mockFinancialData.insurance_cost +
    mockFinancialData.depreciation +
    mockFinancialData.driver_salaries +
    mockFinancialData.other_expenses

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">
          {language === "ar" ? "التقارير المالية" : "Financial Reports"}
        </h2>
        <p className="text-muted-foreground">
          {language === "ar" ? "تحليل التكاليف والأداء المالي" : "Cost analysis and financial performance"}
        </p>
      </div>

      {/* Financial Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {language === "ar" ? "إجمالي التكاليف" : "Total Costs"}
            </CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(totalCosts)}</div>
            <p className="text-xs text-muted-foreground">{language === "ar" ? "هذا الشهر" : "This month"}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{language === "ar" ? "تكاليف الوقود" : "Fuel Costs"}</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(totalFuelCost)}</div>
            <p className="text-xs text-muted-foreground">
              {Math.round((totalFuelCost / totalCosts) * 100)}% {language === "ar" ? "من الإجمالي" : "of total"}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {language === "ar" ? "تكاليف الصيانة" : "Maintenance Costs"}
            </CardTitle>
            <TrendingDown className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(totalMaintenanceCost)}</div>
            <p className="text-xs text-muted-foreground">
              {Math.round((totalMaintenanceCost / totalCosts) * 100)}% {language === "ar" ? "من الإجمالي" : "of total"}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {language === "ar" ? "رواتب السائقين" : "Driver Salaries"}
            </CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(mockFinancialData.driver_salaries)}</div>
            <p className="text-xs text-muted-foreground">
              {Math.round((mockFinancialData.driver_salaries / totalCosts) * 100)}%{" "}
              {language === "ar" ? "من الإجمالي" : "of total"}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Cost Breakdown */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChart className="h-5 w-5" />
              {language === "ar" ? "توزيع التكاليف" : "Cost Breakdown"}
            </CardTitle>
            <CardDescription>
              {language === "ar" ? "توزيع التكاليف حسب الفئة" : "Cost distribution by category"}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm">{language === "ar" ? "رواتب السائقين" : "Driver Salaries"}</span>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full" />
                  <span className="text-sm font-medium">{formatCurrency(mockFinancialData.driver_salaries)}</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">{language === "ar" ? "الوقود" : "Fuel"}</span>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full" />
                  <span className="text-sm font-medium">{formatCurrency(totalFuelCost)}</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">{language === "ar" ? "الصيانة" : "Maintenance"}</span>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-yellow-500 rounded-full" />
                  <span className="text-sm font-medium">{formatCurrency(totalMaintenanceCost)}</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">{language === "ar" ? "الاستهلاك" : "Depreciation"}</span>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full" />
                  <span className="text-sm font-medium">{formatCurrency(mockFinancialData.depreciation)}</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">{language === "ar" ? "التأمين" : "Insurance"}</span>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-purple-500 rounded-full" />
                  <span className="text-sm font-medium">{formatCurrency(mockFinancialData.insurance_cost)}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>{language === "ar" ? "الاتجاهات المالية" : "Financial Trends"}</CardTitle>
            <CardDescription>
              {language === "ar" ? "اتجاه التكاليف خلال الأشهر الماضية" : "Cost trends over recent months"}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {maintenanceCosts.slice(0, 6).map((month: MaintenanceCost, index) => {
                const maxCost = Math.max(...maintenanceCosts.map((m: MaintenanceCost) => m.total_maintenance_cost_egp))
                const percentage = (month.total_maintenance_cost_egp / maxCost) * 100

                return (
                  <div key={month.month_year} className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>{month.month_year}</span>
                      <span className="font-medium">{formatCurrency(month.total_maintenance_cost_egp)}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${percentage}%` }}
                      />
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* ROI and Efficiency Metrics */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle className="text-base">{language === "ar" ? "العائد على الاستثمار" : "ROI"}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">15.8%</div>
            <p className="text-xs text-muted-foreground">
              <TrendingUp className="inline h-3 w-3 mr-1" />
              {language === "ar" ? "زيادة 2.3%" : "2.3% increase"}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-base">{language === "ar" ? "التكلفة لكل كيلومتر" : "Cost per KM"}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(2.3)}</div>
            <p className="text-xs text-muted-foreground">
              <TrendingDown className="inline h-3 w-3 mr-1" />
              {language === "ar" ? "انخفاض 0.2 ج.م" : "0.2 EGP decrease"}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-base">
              {language === "ar" ? "الكفاءة التشغيلية" : "Operational Efficiency"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">87.5%</div>
            <p className="text-xs text-muted-foreground">
              <TrendingUp className="inline h-3 w-3 mr-1" />
              {language === "ar" ? "تحسن 3.2%" : "3.2% improvement"}
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
