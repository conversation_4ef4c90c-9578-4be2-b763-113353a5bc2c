const fs = require('fs');
const path = require('path');

// Read the SQL file
const sqlPath = path.join(__dirname, 'upcoming_services.sql');
const sql = fs.readFileSync(sqlPath, 'utf8');

console.log('To create the upcoming_services view, please follow these steps:');
console.log('');
console.log('1. Go to your Supabase project dashboard');
console.log('2. Navigate to SQL Editor in the left sidebar');
console.log('3. Create a new query');
console.log('4. Paste the following SQL into the editor:');
console.log('');
console.log('--- SQL START ---');
console.log(sql);
console.log('--- SQL END ---');
console.log('');
console.log('5. Click "Run" to execute the query');
console.log('');
console.log('This will create the upcoming_services view that your application needs.');