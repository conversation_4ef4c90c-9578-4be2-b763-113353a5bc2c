const path = require('path');
const cwd = process.cwd();
const envPath = path.join(cwd, '.env.local');
console.log('Current working directory:', cwd);
console.log('Attempting to load .env from:', envPath);
require('dotenv').config({ path: envPath });
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');

// Get credentials from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

console.log('Environment loaded. URL:', supabaseUrl ? `${supabaseUrl.substring(0, 20)}...` : 'undefined');
console.log('Key available:', !!supabaseKey);

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials');
  console.error('Please ensure NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY are set in .env.local');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function createUpcomingServicesView() {
  console.log('Creating upcoming_services view...');
  
  try {
    // Read the SQL file
    const sqlPath = path.join(__dirname, 'upcoming_services.sql');
    const sql = fs.readFileSync(sqlPath, 'utf8');
    
    console.log('SQL loaded successfully. Executing CREATE VIEW...');
    
    // Execute the SQL using Supabase's postgres.rpc
    // Note: This requires the anon key to have permissions to create views
    // If this fails, the user may need to run it in the Supabase dashboard or use service role key
    const { data, error } = await supabase.rpc('execute_sql', { sql_query: sql });
    
    if (error) {
      console.error('Error executing SQL via RPC:', error);
      
      // Alternative: Try direct execution if RPC method doesn't exist
      console.log('Trying alternative method...');
      
      // For Supabase, we can use the direct SQL execution if permissions allow
      // This might require running in Supabase dashboard or with service role
      console.log('Alternative execution not available with anon key. Please:');
      console.log('1. Add the SQL to Supabase dashboard SQL editor');
      console.log('2. Or set SUPABASE_SERVICE_ROLE_KEY in .env.local and update this script');
      console.log('3. Or run the SQL manually in Supabase dashboard');
      
      // Try to verify if the view exists
      const { data: checkData, error: checkError } = await supabase
        .from('upcoming_services')
        .select('count')
        .limit(1);
        
      if (checkError && checkError.message.includes('Could not find the table')) {
        console.error('View does not exist. Manual creation required.');
        process.exit(1);
      } else if (checkData) {
        console.log('View already exists or was created successfully');
        process.exit(0);
      }
    } else {
      console.log('Successfully executed SQL via RPC');
    }
    
    // Verify the view was created
    console.log('Verifying view creation...');
    const { data: verifyData, error: verifyError } = await supabase
      .from('upcoming_services')
      .select('vehicle_id, plate_number')
      .limit(1);
    
    if (verifyError) {
      console.error('Verification failed:', verifyError.message);
      if (verifyError.message.includes('Could not find the table')) {
        console.log('\n=== MANUAL CREATION REQUIRED ===');
        console.log('The upcoming_services view could not be created automatically.');
        console.log('Please do one of the following:');
        console.log('1. Go to Supabase Dashboard > SQL Editor');
        console.log('2. Paste the content of scripts/upcoming_services.sql');
        console.log('3. Click RUN to execute the CREATE VIEW statement');
        console.log('4. The view will then be available for queries');
        process.exit(1);
      }
    } else {
      console.log('View verification successful!');
      if (verifyData && verifyData.length > 0) {
        console.log('View contains data:', verifyData[0]);
      } else {
        console.log('View created successfully but no data found (normal if no upcoming services)');
      }
    }
    
  } catch (err) {
    console.error('Error:', err);
    process.exit(1);
  }
}

createUpcomingServicesView();