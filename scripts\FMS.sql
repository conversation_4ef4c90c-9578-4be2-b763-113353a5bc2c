﻿﻿-- WARNING: This schema is for context only and is not meant to be run.
-- Table order and constraints may not be valid for execution.

CREATE TABLE public.alerts (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  vehicle_id uuid,
  driver_id uuid,
  alert_type character varying NOT NULL,
  alert_type_ar character varying,
  severity USER-DEFINED NOT NULL DEFAULT 'medium'::alert_severity_enum,
  message text NOT NULL,
  message_ar text,
  acknowledged boolean DEFAULT false,
  acknowledged_by uuid,
  acknowledged_at timestamp with time zone,
  resolved boolean DEFAULT false,
  resolved_by uuid,
  resolved_at timestamp with time zone,
  metadata jsonb DEFAULT '{}'::jsonb,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT alerts_pkey PRIMARY KEY (id),
  CONSTRAINT alerts_vehicle_id_fkey FOREIGN KEY (vehicle_id) REFERENCES public.vehicles(id),
  CONSTRAINT alerts_resolved_by_fkey FOREIGN KEY (resolved_by) REFERENCES public.users(id),
  CONSTRAINT alerts_acknowledged_by_fkey FOREIGN KEY (acknowledged_by) REFERENCES public.users(id)
);
CREATE TABLE public.audit_logs (
  id bigint GENERATED ALWAYS AS IDENTITY NOT NULL,
  table_name character varying NOT NULL,
  record_id uuid,
  action character varying NOT NULL,
  old_data jsonb,
  new_data jsonb,
  changed_by uuid,
  changed_at timestamp with time zone DEFAULT now(),
  CONSTRAINT audit_logs_pkey PRIMARY KEY (id),
  CONSTRAINT audit_logs_changed_by_fkey FOREIGN KEY (changed_by) REFERENCES public.users(id)
);
CREATE TABLE public.branches (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  brand_id uuid,
  name character varying NOT NULL,
  location character varying,
  manager_name character varying,
  contact_phone character varying,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT branches_pkey PRIMARY KEY (id),
  CONSTRAINT branches_brand_id_fkey FOREIGN KEY (brand_id) REFERENCES public.brands(id)
);
CREATE TABLE public.brands (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name character varying NOT NULL,
  description text,
  logo_url text,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT brands_pkey PRIMARY KEY (id)
);
CREATE TABLE public.dashboard_configurations (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  level_type character varying NOT NULL,
  level_id uuid,
  config_name character varying NOT NULL,
  config_value jsonb NOT NULL,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT dashboard_configurations_pkey PRIMARY KEY (id)
);
CREATE TABLE public.dashboard_widgets (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  level_type character varying NOT NULL,
  level_id uuid,
  widget_type character varying NOT NULL,
  widget_config jsonb NOT NULL,
  display_order integer NOT NULL DEFAULT 0,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT dashboard_widgets_pkey PRIMARY KEY (id)
);
CREATE TABLE public.driver_documents (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  driver_id uuid NOT NULL,
  document_type character varying NOT NULL,
  document_name character varying NOT NULL,
  file_url text NOT NULL,
  file_name character varying NOT NULL,
  file_size bigint,
  mime_type character varying,
  is_verified boolean DEFAULT false,
  verified_by uuid,
  verified_at timestamp with time zone,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT driver_documents_pkey PRIMARY KEY (id),
  CONSTRAINT driver_documents_verified_by_fkey FOREIGN KEY (verified_by) REFERENCES auth.users(id)
);
CREATE TABLE public.driver_violations (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  driver_id uuid NOT NULL,
  violation_type_id uuid NOT NULL,
  violation_date timestamp with time zone NOT NULL,
  description text,
  penalty_amount numeric DEFAULT 0,
  penalty_status character varying DEFAULT 'pending'::character varying,
  accident_severity character varying,
  points_deducted numeric DEFAULT 0,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT driver_violations_pkey PRIMARY KEY (id),
  CONSTRAINT driver_violations_violation_type_id_fkey FOREIGN KEY (violation_type_id) REFERENCES public.violation_types(id)
);
CREATE TABLE public.drivers (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  full_name character varying NOT NULL,
  national_id character varying UNIQUE,
  license_number character varying NOT NULL UNIQUE,
  license_expiry date NOT NULL,
  phone character varying NOT NULL,
  emergency_phone character varying,
  tourism_permit boolean DEFAULT false,
  airport_permit boolean DEFAULT false,
  performance_score numeric DEFAULT 5.0,
  branch_id uuid,
  license_image_url text,
  license_image_storage_type character varying DEFAULT 'url'::character varying,
  created_at timestamp with time zone DEFAULT now(),
  last_performance_update timestamp with time zone,
  CONSTRAINT drivers_pkey PRIMARY KEY (id),
  CONSTRAINT drivers_branch_id_fkey FOREIGN KEY (branch_id) REFERENCES public.branches(id)
);
CREATE TABLE public.expenses (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  vehicle_id uuid,
  expense_type character varying NOT NULL,
  amount_egp numeric NOT NULL CHECK (amount_egp >= 0::numeric),
  expense_date date NOT NULL DEFAULT CURRENT_DATE,
  notes text,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT expenses_pkey PRIMARY KEY (id),
  CONSTRAINT expenses_vehicle_id_fkey FOREIGN KEY (vehicle_id) REFERENCES public.vehicles(id)
);
CREATE TABLE public.fuel_logs (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  vehicle_id uuid NOT NULL,
  driver_id uuid,
  fuel_date date NOT NULL DEFAULT CURRENT_DATE,
  liters numeric NOT NULL CHECK (liters > 0::numeric),
  cost_egp numeric NOT NULL CHECK (cost_egp >= 0::numeric),
  fuel_station character varying,
  km_at_refill integer,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT fuel_logs_pkey PRIMARY KEY (id),
  CONSTRAINT fuel_logs_vehicle_id_fkey FOREIGN KEY (vehicle_id) REFERENCES public.vehicles(id)
);
CREATE TABLE public.locations (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  vehicle_id uuid NOT NULL,
  timestamp timestamp with time zone NOT NULL DEFAULT now(),
  lat numeric NOT NULL,
  lng numeric NOT NULL,
  speed_kph numeric,
  heading_degrees numeric,
  source character varying DEFAULT 'gps'::character varying,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT locations_pkey PRIMARY KEY (id),
  CONSTRAINT locations_vehicle_id_fkey FOREIGN KEY (vehicle_id) REFERENCES public.vehicles(id)
);
CREATE TABLE public.maintenance (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  vehicle_id uuid NOT NULL,
  service_date date NOT NULL,
  service_type USER-DEFINED NOT NULL,
  description text,
  current_km integer,
  severity USER-DEFINED,
  vendor_id uuid,
  labor_cost_egp numeric DEFAULT 0,
  parts_cost_egp numeric DEFAULT 0,
  total_cost_egp numeric DEFAULT (labor_cost_egp + parts_cost_egp),
  notes text,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT maintenance_pkey PRIMARY KEY (id),
  CONSTRAINT maintenance_vehicle_id_fkey FOREIGN KEY (vehicle_id) REFERENCES public.vehicles(id),
  CONSTRAINT maintenance_vendor_id_fkey FOREIGN KEY (vendor_id) REFERENCES public.vendors(id)
);
CREATE TABLE public.maintenance_rules (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  code character varying NOT NULL,
  vehicle_type character varying,
  fuel_type character varying,
  check_type character varying NOT NULL,
  service_type character varying NOT NULL,
  value integer NOT NULL,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT maintenance_rules_pkey PRIMARY KEY (id)
);
CREATE TABLE public.reports (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name character varying NOT NULL,
  description text,
  report_type USER-DEFINED NOT NULL DEFAULT 'custom'::report_type_enum,
  last_generated timestamp with time zone,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT reports_pkey PRIMARY KEY (id)
);
CREATE TABLE public.tires (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  vehicle_id uuid,
  tire_brand character varying,
  tire_model character varying,
  tire_size character varying NOT NULL,
  tire_type character varying DEFAULT 'regular'::character varying,
  position character varying,
  purchase_date date,
  installation_date date,
  installation_km integer,
  current_km integer,
  max_km integer,
  tread_depth_mm numeric,
  min_tread_depth_mm numeric DEFAULT 1.6,
  pressure_psi integer,
  recommended_pressure_psi integer,
  status character varying DEFAULT 'active'::character varying,
  condition character varying DEFAULT 'good'::character varying,
  purchase_price numeric,
  vendor_id uuid,
  warranty_months integer,
  notes text,
  created_by uuid,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT tires_pkey PRIMARY KEY (id),
  CONSTRAINT tires_vehicle_id_fkey FOREIGN KEY (vehicle_id) REFERENCES public.vehicles(id),
  CONSTRAINT tires_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id),
  CONSTRAINT tires_vendor_id_fkey FOREIGN KEY (vendor_id) REFERENCES public.vendors(id)
);
CREATE TABLE public.trips (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  vehicle_id uuid NOT NULL,
  driver_id uuid NOT NULL,
  trip_number character varying,
  start_location character varying,
  end_location character varying,
  start_time timestamp with time zone,
  end_time timestamp with time zone,
  start_km integer,
  end_km integer,
  distance_km integer DEFAULT 
CASE
    WHEN ((end_km IS NOT NULL) AND (start_km IS NOT NULL)) THEN (end_km - start_km)
    ELSE NULL::integer
END,
  status USER-DEFINED NOT NULL DEFAULT 'planned'::trip_status_enum,
  purpose character varying,
  notes text,
  fuel_consumed_liters numeric,
  created_by uuid,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT trips_pkey PRIMARY KEY (id),
  CONSTRAINT trips_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id),
  CONSTRAINT trips_vehicle_id_fkey FOREIGN KEY (vehicle_id) REFERENCES public.vehicles(id)
);
CREATE TABLE public.users (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  full_name character varying NOT NULL,
  email character varying NOT NULL UNIQUE,
  password_hash character varying,
  role USER-DEFINED NOT NULL DEFAULT 'viewer'::user_role_enum,
  job_title character varying,
  branch_id uuid,
  user_status USER-DEFINED DEFAULT 'active'::user_status_enum,
  last_login timestamp with time zone,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT users_pkey PRIMARY KEY (id),
  CONSTRAINT users_branch_id_fkey FOREIGN KEY (branch_id) REFERENCES public.branches(id)
);
CREATE TABLE public.vehicle_assignments (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  vehicle_id uuid NOT NULL,
  driver_id uuid NOT NULL,
  start_date date NOT NULL DEFAULT CURRENT_DATE,
  end_date date,
  assignment_notes text,
  assigned_by uuid,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT vehicle_assignments_pkey PRIMARY KEY (id),
  CONSTRAINT vehicle_assignments_assigned_by_fkey FOREIGN KEY (assigned_by) REFERENCES public.users(id),
  CONSTRAINT vehicle_assignments_vehicle_id_fkey FOREIGN KEY (vehicle_id) REFERENCES public.vehicles(id)
);
CREATE TABLE public.vehicle_documents (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  vehicle_id uuid NOT NULL,
  document_type character varying NOT NULL,
  document_type_ar character varying,
  document_number character varying,
  issue_date date,
  expiry_date date,
  issuing_authority character varying,
  issuing_authority_ar character varying,
  file_url text,
  notes text,
  status character varying DEFAULT 'active'::character varying,
  reminder_days integer DEFAULT 30,
  created_by uuid,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT vehicle_documents_pkey PRIMARY KEY (id),
  CONSTRAINT vehicle_documents_vehicle_id_fkey FOREIGN KEY (vehicle_id) REFERENCES public.vehicles(id),
  CONSTRAINT vehicle_documents_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id)
);
CREATE TABLE public.vehicles (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  plate_number character varying NOT NULL UNIQUE,
  color text,
  vehicle_type character varying,
  service_type text,
  fuel_type text,
  year integer,
  vin character varying,
  current_km integer DEFAULT 0,
  last_maintenance_km integer,
  next_maintenance_km integer,
  next_maintenance_date date,
  last_tire_change_km integer,
  next_tire_change_km integer,
  next_tire_change_date date,
  insurance_expiry date,
  license_expiry date,
  status USER-DEFINED DEFAULT 'active'::vehicle_status_enum,
  branch_id uuid,
  created_at timestamp with time zone DEFAULT now(),
  license_type character varying,
  vehicle_features character varying,
  permits character varying,
  license_front_image text CHECK (validate_license_image_url(license_front_image)),
  license_back_image text CHECK (validate_license_image_url(license_back_image)),
  CONSTRAINT vehicles_pkey PRIMARY KEY (id),
  CONSTRAINT vehicles_branch_id_fkey FOREIGN KEY (branch_id) REFERENCES public.branches(id)
);
CREATE TABLE public.vendors (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name character varying NOT NULL,
  contact_info jsonb NOT NULL,
  service_type ARRAY,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT vendors_pkey PRIMARY KEY (id)
);
CREATE TABLE public.violation_types (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name character varying NOT NULL,
  description text,
  severity_score numeric NOT NULL,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT violation_types_pkey PRIMARY KEY (id)
);