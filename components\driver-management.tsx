"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Users, Search, Filter, Edit } from "lucide-react"
import { useDrivers } from "@/hooks/use-fleet-data"
import { LoadingSpinner } from "@/components/loading-spinner"
import { ErrorDisplay } from "@/components/error-display"
import { AddDriverDialog } from "@/components/forms/add-driver-dialog"
import { EditDriverDialog } from "@/components/forms/edit-driver-dialog"
import { useState } from "react"
import type { Driver } from "@/lib/supabase"

interface DriverManagementProps {
  language: "ar" | "en"
}

export function DriverManagement({ language }: DriverManagementProps) {
  const { data: drivers, loading, error } = useDrivers(100)
  const [editingDriver, setEditingDriver] = useState<Driver | null>(null)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)

  if (loading) {
    return <LoadingSpinner className="h-64" />
  }

  if (error) {
    return <ErrorDisplay error={error} />
  }

  const handleEditDriver = (driver: Driver) => {
    if (!driver) return;
    setEditingDriver(driver)
    setIsEditDialogOpen(true)
  }

  const getPerformanceBadge = (score: number | null | undefined) => {
    // Add protection for invalid numbers
    if (score === null || score === undefined || isNaN(score)) {
      return <Badge variant="outline">N/A</Badge>
    }
    
    if (score >= 9) return <Badge variant="default">{score.toFixed(1)}/10</Badge>
    if (score >= 7) return <Badge variant="secondary">{score.toFixed(1)}/10</Badge>
    return <Badge variant="destructive">{score.toFixed(1)}/10</Badge>
  }

  const getLicenseStatus = (expiryDate: string) => {
    // Add protection for invalid dates
    if (!expiryDate) {
      return <Badge variant="outline">{language === "ar" ? "غير محدد" : "Not Set"}</Badge>
    }
    
    const expiry = new Date(expiryDate)
    // Check if date is valid
    if (isNaN(expiry.getTime())) {
      return <Badge variant="outline">{language === "ar" ? "غير محدد" : "Not Set"}</Badge>
    }
    
    const today = new Date()
    const daysUntilExpiry = Math.ceil((expiry.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))

    if (daysUntilExpiry < 0) {
      return <Badge variant="destructive">{language === "ar" ? "منتهية" : "Expired"}</Badge>
    } else if (daysUntilExpiry <= 30) {
      return <Badge variant="secondary">{language === "ar" ? "تنتهي قريباً" : "Expiring Soon"}</Badge>
    }
    return <Badge variant="outline">{language === "ar" ? "سارية" : "Valid"}</Badge>
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">
            {language === "ar" ? "إدارة السائقين" : "Driver Management"}
          </h2>
          <p className="text-muted-foreground">
            {language === "ar" ? "إدارة وتتبع جميع السائقين" : "Manage and track all drivers"}
          </p>
        </div>
        <AddDriverDialog language={language} />
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            {language === "ar" ? "قائمة السائقين" : "Driver List"}
          </CardTitle>
          <CardDescription>
            {language === "ar" ? "جميع السائقين في الأسطول" : "All drivers in the fleet"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 mb-6">
            <div className="flex-1">
              <Label htmlFor="search">{language === "ar" ? "البحث" : "Search"}</Label>
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder={
                    language === "ar" ? "البحث بالاسم أو رقم الرخصة..." : "Search by name or license number..."
                  }
                  className="pl-8"
                />
              </div>
            </div>
            <Button variant="outline">
              <Filter className="mr-2 h-4 w-4" />
              {language === "ar" ? "تصفية" : "Filter"}
            </Button>
          </div>

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{language === "ar" ? "الاسم" : "Name"}</TableHead>
                <TableHead>{language === "ar" ? "رقم الرخصة" : "License Number"}</TableHead>
                <TableHead>{language === "ar" ? "انتهاء الرخصة" : "License Expiry"}</TableHead>
                <TableHead>{language === "ar" ? "الهاتف" : "Phone"}</TableHead>
                <TableHead>{language === "ar" ? "الأداء" : "Performance"}</TableHead>
                <TableHead>{language === "ar" ? "التصاريح" : "Permits"}</TableHead>
                <TableHead>{language === "ar" ? "حالة الرخصة" : "License Status"}</TableHead>
                <TableHead>{language === "ar" ? "الإجراءات" : "Actions"}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {drivers.map((driver) => (
                <TableRow key={driver.id}>
                  <TableCell className="font-medium">{driver.full_name || 'N/A'}</TableCell>
                  <TableCell>{driver.license_number || 'N/A'}</TableCell>
                  <TableCell>
                    {driver.license_expiry ? (() => {
                      try {
                        return new Date(driver.license_expiry).toLocaleDateString(language === "ar" ? "ar-EG" : "en-EG");
                      } catch (e) {
                        return 'Invalid Date';
                      }
                    })() : 'N/A'}
                  </TableCell>
                  <TableCell>{driver.phone || 'N/A'}</TableCell>
                  <TableCell>{driver && getPerformanceBadge(driver.performance_score || 0)}</TableCell>
                  <TableCell>
                    <div className="flex gap-1">
                      {driver.tourism_permit && (
                        <Badge variant="outline">{language === "ar" ? "سياحي" : "Tourism"}</Badge>
                      )}
                      {driver.airport_permit && (
                        <Badge variant="outline">{language === "ar" ? "مطار" : "Airport"}</Badge>
                      )}
                      {(!driver.tourism_permit && !driver.airport_permit) && (
                        <Badge variant="outline">{language === "ar" ? "لا يوجد" : "None"}</Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>{getLicenseStatus(driver.license_expiry)}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button variant="ghost" size="sm" onClick={() => handleEditDriver(driver)}>
                        <Edit className="h-4 w-4" />
                        {language === "ar" ? "تعديل" : "Edit"}
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {editingDriver && (
        <EditDriverDialog
          driver={editingDriver}
          language={language}
          open={isEditDialogOpen}
          onClose={() => setIsEditDialogOpen(false)}
          onDriverUpdated={() => {
            // Refresh driver data if needed
            setIsEditDialogOpen(false)
          }}
        />
      )}
    </div>
  )
}