"use client"

import { useState, useEffect } from "react"
import {
  useQuery,
  useQueryClient,
} from "@tanstack/react-query"
import {
  type DashboardOverviewAll,
  type FleetOverview,
  type Vehicle,
  type Driver,
  type User,
  type Alert,
  type UpcomingService,
  type MaintenanceStatistics,
} from "@/lib/supabase"
import { createClient } from "@/lib/supabase-browser"

export interface FuelTrend {
  fuel_date: string;
  total_liters: number;
  total_cost_egp: number;
}

export interface MaintenanceCost {
  month_year: string;
  total_maintenance_cost_egp: number;
  service_count: number;
}

export function useFleetKPIs() {
  const queryClient = useQueryClient()
  
  const { data, isLoading, error } = useQuery({
    queryKey: ['fleet-kpis'],
    queryFn: async () => {
      const supabase = createClient()
      const { data: kpis, error } = await supabase.from("mv_dashboard_overview_all").select("*").single()
      
      if (error) {
        // Check if the error is due to the view not existing
        if (error.message && error.message.includes('relation "mv_dashboard_overview_all" does not exist')) {
          console.warn("Materialized view mv_dashboard_overview_all does not exist. Falling back to mock data.")
          // Return mock data as fallback
          return {
            dashboard_level: "system",
            filter_id: null,
            filter_name: "All Fleet",
            total_vehicles: 0,
            active_vehicles: 0,
            monthly_operating_cost: 0,
            fuel_efficiency_km_per_liter: 0,
            cost_per_kilometer: 0,
            fleet_utilization_rate: 0,
            driver_performance: 0,
            overdue_maintenance: 0,
            insurance_claims_pending: 0,
            maintenance_compliance: 0,
            roi_percentage: 0,
            last_updated: new Date().toISOString()
          }
        }
        throw error
      }
      return kpis
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })

  return {
    data: data as DashboardOverviewAll | null,
    loading: isLoading,
    error: error?.message || null
  }
}

export function useFleetOverview() {
  const queryClient = useQueryClient()
  
  const { data, isLoading, error } = useQuery({
    queryKey: ['fleet-overview'],
    queryFn: async () => {
      const supabase = createClient()
      const { data: overview, error } = await supabase.from("mv_fleet_overview").select("*").single()
      
      if (error) {
        // Check if the error is due to the view not existing
        if (error.message && error.message.includes('relation "mv_fleet_overview" does not exist')) {
          console.warn("Materialized view mv_fleet_overview does not exist. Falling back to mock data.")
          // Return mock data as fallback
          return {
            fleet_type: "all",
            total_vehicles: 0,
            active_vehicles: 0,
            inactive_vehicles: 0,
            maintenance_vehicles: 0,
            out_of_service_vehicles: 0,
            current_month_fuel_liters: 0,
            current_month_fuel_cost: 0,
            previous_month_fuel_liters: 0,
            vehicles_with_scheduled_maintenance: 0,
            overdue_maintenance_vehicles: 0,
            maintenance_compliance_percentage: 0,
            avg_driver_performance: 0,
            excellent_drivers: 0,
            good_drivers: 0,
            poor_drivers: 0,
            total_drivers: 0,
            monthly_fuel_costs: 0,
            monthly_maintenance_costs: 0,
            monthly_other_expenses: 0,
            fleet_fuel_efficiency_km_per_liter: 0,
            total_distance_traveled: 0,
            avg_vehicle_mileage: 0,
            last_updated: new Date().toISOString()
          }
        }
        throw error
      }
      return overview
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })

  return {
    data: data as FleetOverview | null,
    loading: isLoading,
    error: error?.message || null
  }
}

export function useMaintenanceStatistics() {
  const queryClient = useQueryClient()
  
  const { data, isLoading, error } = useQuery({
    queryKey: ['maintenance-statistics'],
    queryFn: async () => {
      const supabase = createClient()
      const { data: stats, error } = await supabase.from("mv_maintenance_statistics").select("*").single()
      
      if (error) {
        // Check if the error is due to the view not existing
        if (error.message && error.message.includes('relation "mv_maintenance_statistics" does not exist')) {
          console.warn("Materialized view mv_maintenance_statistics does not exist. Falling back to mock data.")
          // Return mock data as fallback
          return {
            stats_type: "all",
            total_records: 0,
            total_cost_ytd: 0,
            average_cost: 0,
            service_types_count: 0,
            unique_vehicles: 0,
            parts_cost_ytd: 0,
            labor_cost_ytd: 0,
            critical_records: 0,
            service_centers_count: 0,
            next_service_due: 0,
            last_maintenance_date: null,
            avg_days_between_maintenance: 0,
            most_common_service_type: null,
            last_updated: new Date().toISOString()
          }
        }
        throw error
      }
      return stats
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
  })

  return {
    data: data as MaintenanceStatistics | null,
    loading: isLoading,
    error: error?.message || null
  }
}

export function useVehicles(limit = 50) {
  const queryClient = useQueryClient()
  
  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ['vehicles', limit],
    queryFn: async () => {
      const supabase = createClient()
      const { data: vehicles, error } = await supabase
        .from("vehicles")
        .select("id, plate_number, color, vehicle_type, service_type, fuel_type, year, vin, current_km, last_maintenance_km, next_maintenance_km, last_tire_change_km, license_expiry, branch_id, status, created_at, license_type, vehicle_features, permits")
        .order("created_at", { ascending: false })
        .limit(limit)

      if (error) throw error
      return vehicles || []
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  })

  return {
    data: data as Vehicle[],
    loading: isLoading,
    error: error?.message || null,
    refetch
  }
}

export function useDrivers(limit = 50) {
  const queryClient = useQueryClient()
  
  const { data, isLoading, error } = useQuery({
    queryKey: ['drivers', limit],
    queryFn: async () => {
      const supabase = createClient()
      const { data: drivers, error } = await supabase
        .from("drivers")
        .select("id, full_name, national_id, license_number, license_expiry, phone, emergency_phone, tourism_permit, airport_permit, performance_score, branch_id, license_image_url, license_image_storage_type, created_at, last_performance_update")
        .order("performance_score", { ascending: false })
        .limit(limit)

      if (error) throw error
      return drivers || []
    },
    staleTime: 3 * 60 * 1000, // 3 minutes
    gcTime: 7 * 60 * 1000, // 7 minutes
  })

  return {
    data: data as Driver[],
    loading: isLoading,
    error: error?.message || null
  }
}

export function useRecentAlerts(limit = 20) {
  const queryClient = useQueryClient()
  
  const { data, isLoading, error } = useQuery({
    queryKey: ['recent-alerts', limit],
    queryFn: async () => {
      const supabase = createClient()
      const { data: alerts, error } = await supabase
        .from("alerts")
        .select("*")
        .order("created_at", { ascending: false })
        .limit(limit)

      if (error) throw error
      return alerts || []
    },
    staleTime: 30 * 1000, // 30 seconds for alerts
    gcTime: 2 * 60 * 1000, // 2 minutes
  })

  return {
    data: data as Alert[],
    loading: isLoading,
    error: error?.message || null
  }
}

// Hook to fetch upcoming services with manual refresh capability
export function useUpcomingServices(limit = 50) {
  const queryClient = useQueryClient()
  
  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ['upcoming-services', limit],
    queryFn: async () => {
      const supabase = createClient()
      
      // Try to refresh the materialized view
      // Note: refresh_materialized_views RPC function doesn't exist, using refresh_dashboard_views if available
      // const { error: refreshError } = await supabase.rpc('refresh_materialized_views')
      
      // Fetch the data
      const { data: services, error } = await supabase.from("mv_upcoming_services").select("*").limit(limit)

      if (error) {
        // Check if the error is due to the view not existing
        if (error.message && (error.message.includes('relation "mv_upcoming_services" does not exist') || error.message.includes('mv_upcoming_services'))) {
          console.warn("Materialized view mv_upcoming_services does not exist. Falling back to regular query.")
          // Fallback to a regular query on the vehicles table with similar logic
          const { data: fallbackData, error: fallbackError } = await supabase
            .from("vehicles")
            .select(`
              id, 
              vehicle_type, 
              plate_number, 
              current_km, 
              next_maintenance_date, 
              next_maintenance_km, 
              next_tire_change_date,
              next_tire_change_km,
              license_expiry, 
              insurance_expiry,
              status
            `)
            .limit(limit)
            
          if (fallbackError) {
            console.error("Fallback query also failed:", fallbackError)
            throw new Error(`Failed to fetch data: ${fallbackError.message || JSON.stringify(fallbackError)}`)
          }
          
          // Transform the fallback data to match the UpcomingService interface
          const transformedData: UpcomingService[] = (fallbackData || []).map((item: any) => {
            // Collect all possible service types and their dates
            const services = [
              { type: 'maintenance', date: item.next_maintenance_date, km: item.next_maintenance_km },
              { type: 'tire_change', date: item.next_tire_change_date, km: item.next_tire_change_km },
              { type: 'license_expiry', date: item.license_expiry, km: null },
              { type: 'insurance_expiry', date: item.insurance_expiry, km: null }
            ].filter(service => service.date || service.km); // Only include services with date or km
            
            // If no services found, create a default entry
            if (services.length === 0) {
              return {
                vehicle_id: item.id,
                vehicle_type: item.vehicle_type || null,
                plate_number: item.plate_number,
                current_km: item.current_km || null,
                service_type: 'unknown',
                due_date: null,
                due_km: null,
                days_until_due: null,
                km_until_due: null,
                priority: 'unknown',
                vehicle_status: item.status || 'active',
                last_updated: new Date().toISOString()
              };
            }
            
            // For now, just take the first service (in a real implementation, you might want to prioritize or show all)
            const service = services[0];
            let daysUntilDue: number | null = null;
            let kmUntilDue: number | null = null;
            
            // Calculate days until due if we have a due date
            if (service.date) {
              const dueDateObj = new Date(service.date);
              const today = new Date();
              // Check if the date is valid
              if (!isNaN(dueDateObj.getTime())) {
                daysUntilDue = Math.ceil((dueDateObj.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
              }
            }
            
            // Calculate km until due if we have due km and current km
            if (service.km && item.current_km) {
              kmUntilDue = service.km - item.current_km;
            }
            
            // Determine priority based on days and km
            let priority = 'normal';
            if ((daysUntilDue !== null && daysUntilDue <= 7) || (kmUntilDue !== null && kmUntilDue <= 500)) {
              priority = 'urgent';
            } else if ((daysUntilDue !== null && daysUntilDue <= 30) || (kmUntilDue !== null && kmUntilDue <= 1000)) {
              priority = 'important';
            }
            
            return {
              vehicle_id: item.id,
              vehicle_type: item.vehicle_type || null,
              plate_number: item.plate_number,
              current_km: item.current_km || null,
              service_type: service.type,
              due_date: service.date || null,
              due_km: service.km || null,
              days_until_due: daysUntilDue,
              km_until_due: kmUntilDue,
              priority: priority,
              vehicle_status: item.status || 'active',
              last_updated: new Date().toISOString()
            };
          });
          
          return transformedData
        } else {
          console.error("Supabase error details:", error)
          throw new Error(`Database error: ${error.message || JSON.stringify(error)}`)
        }
      } else {
        return services || []
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })

  return {
    data: data as UpcomingService[] || [],
    loading: isLoading,
    error: error?.message || null,
    refreshServices: refetch
  }
}

export function useFuelTrends(days = 30) {
  const queryClient = useQueryClient()
  
  const { data, isLoading, error } = useQuery({
    queryKey: ['fuel-trends', days],
    queryFn: async () => {
      const supabase = createClient()
      const endDate = new Date()
      const startDate = new Date()
      startDate.setDate(startDate.getDate() - days)

      const { data: fuelLogs, error } = await supabase
        .from("fuel_logs")
        .select("fuel_date, liters, cost_egp")
        .gte("fuel_date", startDate.toISOString().split("T")[0])
        .lte("fuel_date", endDate.toISOString().split("T")[0])
        .order("fuel_date", { ascending: true })

      if (error) throw error

      // Group by date and sum values
      const groupedData = (fuelLogs || []).reduce((acc: { [key: string]: FuelTrend }, log: { fuel_date: string; liters: number; cost_egp: number }) => {
        const date = log.fuel_date
        if (!acc[date]) {
          acc[date] = { fuel_date: date, total_liters: 0, total_cost_egp: 0 }
        }
        acc[date].total_liters += log.liters
        acc[date].total_cost_egp += log.cost_egp
        return acc
      }, {})

      return Object.values(groupedData)
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 20 * 60 * 1000, // 20 minutes
  })

  return {
    data: data as FuelTrend[] || [],
    loading: isLoading,
    error: error?.message || null
  }
}

export function useMaintenanceCosts(months = 12) {
  const queryClient = useQueryClient()
  
  const { data, isLoading, error } = useQuery({
    queryKey: ['maintenance-costs', months],
    queryFn: async () => {
      const supabase = createClient()
      const endDate = new Date()
      const startDate = new Date()
      startDate.setMonth(startDate.getMonth() - months)

      const { data: maintenance, error } = await supabase
        .from("maintenance")
        .select("service_date, total_cost_egp, service_type")
        .gte("service_date", startDate.toISOString().split("T")[0])
        .lte("service_date", endDate.toISOString().split("T")[0])
        .order("service_date", { ascending: true })

      if (error) throw error

      // Group by month and sum costs
      const groupedData = (maintenance || []).reduce((acc: { [key: string]: MaintenanceCost }, record: { service_date: string; total_cost_egp: number; service_type: string }) => {
        const monthYear = record.service_date.substring(0, 7) // YYYY-MM
        if (!acc[monthYear]) {
          acc[monthYear] = { month_year: monthYear, total_maintenance_cost_egp: 0, service_count: 0 }
        }
        acc[monthYear].total_maintenance_cost_egp += record.total_cost_egp || 0
        acc[monthYear].service_count += 1
        return acc
      }, {})

      return Object.values(groupedData)
    },
    staleTime: 15 * 60 * 1000, // 15 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  })

  return {
    data: data as MaintenanceCost[] || [],
    loading: isLoading,
    error: error?.message || null
  }
}

export function useVehicleStatusDistribution() {
  const queryClient = useQueryClient()
  
  
  const { data, isLoading, error } = useQuery({
    queryKey: ['vehicle-status-distribution'],
    queryFn: async () => {
      const supabase = createClient()
      const { data: vehicles, error } = await supabase.from("vehicles").select("status")

      if (error) throw error

      // Group by status and count
      const statusCounts = (vehicles || []).reduce((acc: any, vehicle: any) => {
        const status = vehicle.status || "unknown"
        acc[status] = (acc[status] || 0) + 1
        return acc
      }, {})

      const total = Object.values(statusCounts).reduce((sum: number, count: any) => sum + count, 0)

      const distribution = Object.entries(statusCounts).map(([status, count]: [string, any]) => ({
        status,
        count,
        percentage: total > 0 ? Math.round((count / total) * 100) : 0,
      }))

      return distribution
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })

  return {
    data: data || [],
    loading: isLoading,
    error: error?.message || null
  }
}

export function useUsers(limit = 50) {
  const queryClient = useQueryClient()
  
  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ['users', limit],
    queryFn: async () => {
      const supabase = createClient()
      const { data: users, error } = await supabase
        .from("users")
        .select(`
          *,
          branch:branches(name)
        `)
        .order("created_at", { ascending: false })
        .limit(limit)

      if (error) throw error
      return users || []
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  })

  return {
    data: data as User[],
    loading: isLoading,
    error: error?.message || null,
    refetch
  }
}

// Hook to fetch current authenticated user's profile data
export function useCurrentUserProfile() {
  const [data, setData] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [userEmail, setUserEmail] = useState<string | null>(null)
  const [userId, setUserId] = useState<string | null>(null)

  // Create browser client for auth operations
  const browserClient = createClient()

  // Get current user's ID and email from auth session
  useEffect(() => {
    async function getCurrentUser() {
      try {
        const { data: { session } } = await browserClient.auth.getSession()
        if (session?.user) {
          setUserId(session.user.id)
          setUserEmail(session.user.email || null)
          console.log('Auth: Session found for', session.user.email)
        } else {
          setUserId(null)
          setUserEmail(null)
          console.log('Auth: No session found')
        }
      } catch (err) {
        console.error("Error getting current user session:", err)
      }
    }

    getCurrentUser()

    // Listen for auth state changes (minimal logging)
    const { data: { subscription } } = browserClient.auth.onAuthStateChange((_event: any, session: any) => {
      const newUserId = session?.user?.id || null
      const newUserEmail = session?.user?.email || null

      // Only update if something actually changed
      if (newUserId !== userId || newUserEmail !== userEmail) {
        setUserId(newUserId)
        setUserEmail(newUserEmail)

        if (!newUserId) {
          setData(null)
          console.log('Auth: User logged out')
        } else if (newUserId !== userId) {
          console.log('Auth: User changed to', newUserEmail)
        }
      }
    })

    return () => {
      subscription.unsubscribe()
    }
  }, [])

  // Fetch user profile data when user ID is available
  useEffect(() => {
    async function fetchCurrentUserProfile() {
      if (!userId) {
        setLoading(false)
        return
      }

      try {
        setLoading(true)
        setError(null)
        
        console.log('Fetching user profile for ID:', userId)
        
        // Use User_ID for optimal performance and security
        const supabase = createClient()
        // Avoid selecting relational data that may trigger recursive RLS evaluations
        // Fetch only needed columns from users first
        const { data: user, error } = await supabase
          .from("users")
          .select("id, email, full_name, role, branch_id, created_at, updated_at")
          .eq("id", userId)
          .maybeSingle()

        if (error) {
          console.error('Database query error:', error)

          // Handle specific error types
          if (error.message && error.message.includes('infinite recursion detected')) {
            console.error('RLS policy infinite recursion detected. This may be due to problematic policies.')
            throw new Error('Database configuration error. Please contact administrator.')
          } else if (error.message && error.message.includes('row-level security policy')) {
            console.error('RLS policy violation:', error.message)
            throw new Error('Access denied. Please check your permissions.')
          } else {
            throw new Error(`Database error: ${error.message || 'Unknown database error'}`)
          }
        }

        if (!user) {
          console.warn('No user record found for ID:', userId)
          // For security, don't expose the actual user ID in error messages
          setData(null)
          setError(`No user profile found. Please contact administrator.`)
        } else {
          console.log('Profile loaded for:', user.full_name || user.email)
          setData(user)
          setError(null)
        }
      } catch (err) {
        console.error("Error fetching current user profile:", err)
        const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
        setError(errorMessage)
        setData(null)
      } finally {
        setLoading(false)
      }
    }

    fetchCurrentUserProfile()
  }, [userId])

  return { data, loading, error, userEmail, userId }
}
