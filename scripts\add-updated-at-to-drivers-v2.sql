-- Add updated_at column to drivers table
-- This column is required by the application for proper Supabase operations

-- Add the updated_at column if it doesn't exist
ALTER TABLE public.drivers 
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Add a comment to explain the column purpose
COMMENT ON COLUMN public.drivers.updated_at IS 'Timestamp of last update - تحديث آخر تعديل';

-- Update existing records to set the updated_at value to created_at or current time
UPDATE public.drivers 
SET updated_at = COALESCE(created_at, NOW()) 
WHERE updated_at IS NULL;

-- Create or replace the function to update the updated_at column
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop the trigger if it exists
DROP TRIGGER IF EXISTS update_drivers_updated_at ON public.drivers;

-- Create trigger for drivers table
CREATE TRIGGER update_drivers_updated_at 
    BEFORE UPDATE ON public.drivers 
    FOR EACH ROW 
    EXECUTE FUNCTION public.update_updated_at_column();

-- Verification query - check that the column and trigger exist
-- SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'drivers' AND column_name = 'updated_at';
-- SELECT tgname FROM pg_trigger WHERE tgname = 'update_drivers_updated_at';