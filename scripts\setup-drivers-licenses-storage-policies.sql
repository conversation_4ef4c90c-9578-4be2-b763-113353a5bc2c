-- Setup storage policies for Drivers-licenses bucket to allow any authenticated user
-- to upload, update, and delete driver license images

-- First, ensure the bucket exists
-- Note: This command needs to be run in the Supabase SQL editor
-- INSERT INTO storage.buckets (id, name, public) 
-- VALUES ('Drivers-licenses', 'Drivers-licenses', true)
-- ON CONFLICT (id) DO UPDATE SET public = true;

-- Allow anyone to view public images (SELECT)
DROP POLICY IF EXISTS "Allow public read access to driver licenses" ON storage.objects;
CREATE POLICY "Allow public read access to driver licenses"
ON storage.objects FOR SELECT
USING ( bucket_id = 'Drivers-licenses' );

-- Allow ANY authenticated user to upload images (INSERT)
-- This policy allows any authenticated user to upload driver license images
DROP POLICY IF EXISTS "Allow any authenticated user to upload driver licenses" ON storage.objects;
CREATE POLICY "Allow any authenticated user to upload driver licenses"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK ( bucket_id = 'Drivers-licenses' );

-- Allow ANY authenticated user to update driver licenses (UPDATE)
-- This policy allows any authenticated user to update ANY driver license image
-- Less secure but meets your current requirement of allowing any user to edit
DROP POLICY IF EXISTS "Allow any authenticated user to update driver licenses" ON storage.objects;
CREATE POLICY "Allow any authenticated user to update driver licenses"
ON storage.objects FOR UPDATE
TO authenticated
USING ( bucket_id = 'Drivers-licenses' )
WITH CHECK ( bucket_id = 'Drivers-licenses' );

-- Allow ANY authenticated user to delete driver licenses (DELETE)
-- This policy allows any authenticated user to delete ANY driver license image
-- Less secure but meets your current requirement of allowing any user to delete
DROP POLICY IF EXISTS "Allow any authenticated user to delete driver licenses" ON storage.objects;
CREATE POLICY "Allow any authenticated user to delete driver licenses"
ON storage.objects FOR DELETE
TO authenticated
USING ( bucket_id = 'Drivers-licenses' );