import { createClient } from '@supabase/supabase-js'

// Get credentials from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials')
  console.error('Please set SUPABASE_SERVICE_ROLE_KEY in your environment variables')
  process.exit(1)
}

// Create Supabase client with service role key for database operations
const supabase = createClient(supabaseUrl, supabaseKey)

async function createUpcomingServicesView() {
  console.log('Creating upcoming_services view...')
  
  // Read the SQL file
  const fs = require('fs')
  const path = require('path')
  
  try {
    const sqlPath = path.join(__dirname, 'upcoming_services.sql')
    const sql = fs.readFileSync(sqlPath, 'utf8')
    
    console.log('Executing SQL:', sql.substring(0, 200) + '...')
    
    // For Supabase, we need to use the proper method to execute SQL
    // Let's try to execute the query directly
    const { error } = await supabase.rpc('exec_sql', { sql })
    
    if (error) {
      console.error('Error creating view with RPC:', error)
      console.log('Trying alternative method...')
      
      // Alternative approach - split the SQL into individual statements
      // and execute them one by one
      const statements = sql.split(';').filter((stmt: string) => stmt.trim().length > 0)
      
      for (const statement of statements) {
        const trimmedStmt: string = statement.trim()
        if (trimmedStmt.length > 0) {
          console.log('Executing statement:', trimmedStmt.substring(0, 100) + '...')
          // Note: This is a simplified approach. In practice, you might need
          // to use a proper SQL parser or execute this through the Supabase dashboard
        }
      }
    } else {
      console.log('Successfully created upcoming_services view')
    }
  } catch (err) {
    console.error('Error:', err)
    process.exit(1)
  }
}

createUpcomingServicesView()