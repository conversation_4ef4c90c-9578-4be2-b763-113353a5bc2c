-- IMMEDIATE FIX for infinite recursion in users table
-- This script provides a quick fix to stop the infinite recursion errors
-- Run this in your Supabase SQL Editor immediately

-- Step 1: Temporarily disable <PERSON><PERSON> on users table to stop the recursion
ALTER TABLE users DISABLE ROW LEVEL SECURITY;

-- Step 2: Drop all existing policies on users table that might be causing issues
DROP POLICY IF EXISTS "Users can access their own data" ON users;
DROP POLICY IF EXISTS "Branch managers can access branch users" ON users;
DROP POLICY IF EXISTS "Admins can access all users" ON users;
DROP POLICY IF EXISTS "Users can view users in same branch" ON users;
DROP POLICY IF EXISTS "Enable read access for all users" ON users;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON users;
DROP POLICY IF EXISTS "Enable update for users based on email" ON users;
DROP POLICY IF EXISTS "Enable delete for users based on email" ON users;

-- Step 3: Re-enable RLS with a simple, safe policy
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Step 4: Create a basic policy that allows users to see their own data only
CREATE POLICY "Users can access own data" ON users
  FOR ALL TO authenticated
  USING (auth.uid() = id);

-- Step 5: Create a policy for service role (admin operations)
CREATE POLICY "Service role full access" ON users
  FOR ALL TO service_role
  USING (true);

-- Step 6: Fix any other tables that might have problematic policies referencing users

-- Check and fix branches table policies
DROP POLICY IF EXISTS "Branch access based on user branch" ON branches;
CREATE POLICY "All authenticated users can read branches" ON branches
  FOR SELECT TO authenticated
  USING (true);

-- Check and fix vehicles table policies  
DROP POLICY IF EXISTS "Vehicle access based on user branch" ON vehicles;
CREATE POLICY "All authenticated users can read vehicles" ON vehicles
  FOR SELECT TO authenticated
  USING (true);

-- Check and fix drivers table policies
DROP POLICY IF EXISTS "Driver access based on user branch" ON drivers;
CREATE POLICY "All authenticated users can read drivers" ON drivers
  FOR SELECT TO authenticated
  USING (true);

-- Note: These policies are permissive for now to stop the recursion
-- You can tighten them later once the immediate issue is resolved

-- Verification: Test that you can now query users without recursion
-- SELECT id, email, full_name, role FROM users LIMIT 5;
