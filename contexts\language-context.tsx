"use client"

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'

type Language = "ar" | "en"

interface Translations {
  [key: string]: {
    [lang: string]: string
  }
}

const translations: Translations = {
  "login.title": { ar: "نظام إدارة الأسطول", en: "Fleet Management System" },
  "login.subtitle": { ar: "تسجيل الدخول إلى لوحة التحكم", en: "Sign in to your dashboard" },
  "login.welcome": { ar: "مرحباً بعودتك!", en: "Welcome Back!" },
  "login.signIn": { ar: "تسجيل الدخول", en: "Sign In" },
  "login.signingIn": { ar: "جاري تسجيل الدخول...", en: "Signing in..." },
  "login.description": { ar: "أدخل بياناتك للوصول إلى حسابك", en: "Enter your credentials to access your account" },
  "login.email": { ar: "الب<PERSON>يد الإلكتروني", en: "Email" },
  "login.emailPlaceholder": { ar: "<EMAIL>", en: "<EMAIL>" },
  "login.password": { ar: "كلمة المرور", en: "Password" },
  "login.passwordPlaceholder": { ar: "********", en: "********" },
  "login.contactAdmin": { ar: "في حالة وجود مشكلة، تواصل مع مسؤول النظام", en: "Having trouble? Contact your system administrator" },
  "login.signingUp": { ar: "جاري إنشاء الحساب...", en: "Creating account..." },
  "login.createAccount": { ar: "إنشاء حساب جديد", en: "Create new account" },
  "signup.title": { ar: "نظام إدارة الأسطول", en: "Fleet Management System" },
  "signup.createAccount": { ar: "إنشاء حساب جديد", en: "Create a new account" },
  "signup.email": { ar: "البريد الإلكتروني", en: "Email" },
  "signup.emailPlaceholder": { ar: "<EMAIL>", en: "<EMAIL>" },
  "signup.password": { ar: "كلمة المرور", en: "Password" },
  "signup.passwordPlaceholder": { ar: "********", en: "********" },
  "signup.confirmPassword": { ar: "تأكيد كلمة المرور", en: "Confirm Password" },
  "signup.confirmPasswordPlaceholder": { ar: "********", en: "********" },
  "signup.createAccountButton": { ar: "إنشاء حساب", en: "Create Account" },
  "signup.creatingAccount": { ar: "جاري إنشاء الحساب...", en: "Creating account..." },
  "signup.passwordMismatch": { ar: "كلمات المرور غير متطابقة", en: "Passwords do not match" },
  "signup.passwordTooShort": { ar: "كلمة المرور قصيرة جداً (6 أحرف على الأقل)", en: "Password is too short (minimum 6 characters)" },
  "signup.successMessage": { ar: "تم إنشاء الحساب بنجاح! يرجى التحقق من بريدك الإلكتروني لتأكيد الحساب.", en: "Account created successfully! Please check your email to confirm your account." },
  "signup.errorMessage": { ar: "حدث خطأ أثناء إنشاء الحساب", en: "An error occurred while creating the account" },
  "signup.databaseErrorMessage": { ar: "حدث خطأ في قاعدة البيانات أثناء إنشاء الحساب.", en: "A database error occurred while creating the account." },
  "signup.tryAgainMessage": { ar: "يرجى المحاولة مرة أخرى أو الاتصال بالمسؤول.", en: "Please try again or contact an administrator." },
  "signup.userExistsMessage": { ar: "هذا البريد الإلكتروني مسجل بالفعل. يرجى تسجيل الدخول بدلاً من ذلك.", en: "This email is already registered. Please sign in instead." },
  "signup.backToLogin": { ar: "العودة إلى تسجيل الدخول", en: "Back to login" },
  "common.dir": { ar: "rtl", en: "ltr" }
}

interface LanguageContextType {
  language: Language
  setLanguage: (language: Language) => void
  t: (key: string) => string
  direction: 'ltr' | 'rtl'
  isRTL: boolean
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined)

export function LanguageProvider({ children }: { children: ReactNode }) {
  const [language, setLanguage] = useState<Language>("en")

  // Derive direction from language
  const direction = language === 'ar' ? 'rtl' : 'ltr'
  const isRTL = language === 'ar'

  const t = (key: string): string => {
    return translations[key]?.[language] || key
  }

  // Update document attributes when language changes
  useEffect(() => {
    // Only run on client-side
    if (typeof document === 'undefined') return
    
    // Update body attributes
    document.body.setAttribute('data-language', language)
    document.body.setAttribute('lang', language)
    
    // Update HTML element attributes for proper RTL/LTR handling
    const htmlElement = document.documentElement
    htmlElement.setAttribute('lang', language)
    htmlElement.setAttribute('dir', direction)
    
    // Update body classes for additional styling hooks
    document.body.classList.remove('rtl', 'ltr')
    document.body.classList.add(direction)
  }, [language, direction])

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t, direction, isRTL }}>
      {children}
    </LanguageContext.Provider>
  )
}

export function useLanguage() {
  const context = useContext(LanguageContext)
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider')
  }
  return context
}