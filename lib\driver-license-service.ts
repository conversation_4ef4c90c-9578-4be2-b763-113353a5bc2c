/**
 * Driver License Image Management Service
 * 
 * خدمة إدارة صور رخص السائقين
 * Comprehensive service for managing driver license images with Supabase Storage
 * 
 * Features:
 * - Upload license images to Supabase Storage
 * - Generate public URLs for images
 * - Update driver records with image URLs
 * - Clean up old images when replacing
 * - Browser-compatible implementation
 * 
 * Author: AI Assistant
 * Date: 2025-09-07
 */

import { createClient } from '@/lib/supabase-browser';

// Types for better TypeScript support
export interface UploadResult {
  success: boolean;
  imageUrl?: string;
  error?: string;
}

export interface DriverLicenseData {
  driverId: string;
  licenseImage?: File;
}

// Type for driver license image data from database
export interface DriverLicenseRecord {
  license_image_url?: string | null;
  license_image_storage_type?: string | null;
}

export type LicenseSide = 'front';

/**
 * Driver License Image Service Class
 * Handles all license image operations with Supabase Storage
 */
export class DriverLicenseService {
  private supabase;
  private readonly BUCKET_NAME = 'Drivers-licenses';
  
  constructor() {
    this.supabase = createClient();
  }

  /**
   * Ensure the Drivers-licenses bucket exists
   * التأكد من وجود bucket الرخص
   */
  private async ensureBucketExists(): Promise<void> {
    try {
      console.log(`🔍 Checking if bucket '${this.BUCKET_NAME}' exists...`);
      
      // Check if bucket exists
      const { data: buckets, error: listError } = await this.supabase.storage.listBuckets();
      
      if (listError) {
        console.warn('⚠️ Could not list buckets:', listError.message);
        // Don't throw error here, continue with upload attempt
        return;
      }

      console.log('📦 Available buckets:', buckets);

      // Fixed the issue with potentially undefined buckets array
      const bucketExists = Array.isArray(buckets) && buckets.some((bucket: { name: string }) => bucket.name === this.BUCKET_NAME);
      
      console.log(`🔍 Bucket '${this.BUCKET_NAME}' exists:`, bucketExists);
      
      if (!bucketExists) {
        console.log(`📦 Creating bucket: ${this.BUCKET_NAME}`);
        
        const { error: createError } = await this.supabase.storage.createBucket(this.BUCKET_NAME, {
          public: true,
          allowedMimeTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
          fileSizeLimit: 5242880 // 5MB
        });

        if (createError) {
          console.warn(`⚠️ Could not create bucket: ${createError.message}`);
          // Don't throw error here, continue with upload attempt
          // The bucket likely needs to be created manually through SQL editor
          console.warn('💡 Hint: Storage buckets should be created manually through the Supabase SQL editor.');
          console.warn('💡 Run the updated script in scripts/create-drivers-licenses-bucket.sql to create the bucket with proper policies.');
        } else {
          console.log(`✅ Bucket ${this.BUCKET_NAME} created successfully`);
        }
      } else {
        console.log(`✅ Bucket ${this.BUCKET_NAME} already exists`);
      }
    } catch (error) {
      console.warn('⚠️ Error checking/creating bucket:', error);
      // Continue anyway - bucket might exist
    }
  }

  /**
   * Upload a license image to Supabase Storage and update driver record
   * رفع صورة رخصة إلى Supabase Storage وتحديث سجل السائق
   * 
   * @param file - Image file to upload
   * @param driverId - Driver ID to associate with the image
   * @returns Promise with upload result including public URL
   */
  async uploadLicenseImage(file: File, driverId: string): Promise<UploadResult> {
    try {
      // Ensure bucket exists before attempting upload
      await this.ensureBucketExists();
      
      // Debug: Check authentication status
      const { data: { user }, error: authError } = await this.supabase.auth.getUser();

      if (!user) {
        throw new Error('المستخدم غير مصادق - User not authenticated. Please log in first.');
      }

      // Validate inputs
      if (!file) {
        throw new Error('ملف الصورة مطلوب - Image file is required');
      }

      if (!driverId) {
        throw new Error('معرف السائق مطلوب - Driver ID is required');
      }

      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        throw new Error('نوع الملف غير مدعوم. يرجى استخدام JPEG, PNG أو WebP - Unsupported file type. Please use JPEG, PNG or WebP');
      }

      // Validate file size (max 5MB)
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (file.size > maxSize) {
        throw new Error('حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت - File too large. Maximum size is 5MB');
      }

      // Remove old image before uploading new one
      await this.removeOldImage(driverId);

      // Generate unique file path
      const timestamp = Date.now();
      const fileExtension = file.name.split('.').pop() || 'jpg';
      const fileName = `license-front_${timestamp}_${file.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`;
      const filePath = `${driverId}/${fileName}`;

      console.log(`📁 Attempting to upload file to: ${this.BUCKET_NAME}/${filePath}`);

      // Upload file to Supabase Storage
      const { data: uploadData, error: uploadError } = await this.supabase.storage
        .from(this.BUCKET_NAME)
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false // Don't overwrite existing files
        });

      if (uploadError) {
        console.error('❌ Upload failed:', uploadError);
        // Check if it's a permissions error
        if (uploadError.message.includes('row-level security policy') || 
            uploadError.message.includes('denied') ||
            uploadError.message.includes('Bucket not found') ||
            uploadError.message.includes('not found') ||
            uploadError.message.includes('42501') ||  // PostgreSQL permission error
            uploadError.message.includes('must be owner')) {
          // Try to create bucket and retry upload
          console.log('🔄 Retrying upload after ensuring bucket exists...');
          await this.ensureBucketExists();
          
          // Retry upload
          const { data: retryData, error: retryError } = await this.supabase.storage
            .from(this.BUCKET_NAME)
            .upload(filePath, file, {
              cacheControl: '3600',
              upsert: false
            });
          
          if (retryError) {
            console.error('❌ Upload failed after retry:', retryError);
            // Provide more specific error messages
            if (retryError.message.includes('Bucket not found')) {
              // Try fallback method to store image in database
              console.log('🔄 Using fallback method to store image in database...');
              return await this.storeImageInDatabaseAsBase64(file, driverId);
            } else if (retryError.message.includes('row-level security policy') || 
                       retryError.message.includes('denied') ||
                       retryError.message.includes('42501') ||
                       retryError.message.includes('must be owner')) {
              // Try fallback method to store image in database
              console.log('🔄 Using fallback method to store image in database due to permissions...');
              console.warn('💡 To fix this issue permanently, run the script in scripts/create-drivers-licenses-bucket.sql in your Supabase SQL editor');
              return await this.storeImageInDatabaseAsBase64(file, driverId);
            } else {
              throw new Error(`Storage error: ${retryError.message}`);
            }
          }
          
          // Use retry data if successful
          console.log('✅ Upload successful on retry:', retryData);
        } else {
          throw new Error(`فشل رفع الملف: ${uploadError.message} - Upload failed: ${uploadError.message}`);
        }
      }

      // Get public URL
      const { data: urlData } = this.supabase.storage
        .from(this.BUCKET_NAME)
        .getPublicUrl(filePath);

      if (!urlData?.publicUrl) {
        throw new Error('فشل في الحصول على رابط الصورة - Failed to get image URL');
      }

      const publicUrl = urlData.publicUrl;

      // Update driver record with new image URL
      const updateResult = await this.updateDriverImageUrl(driverId, publicUrl);
      
      if (!updateResult.success) {
        // If database update fails, try to clean up the uploaded file
        await this.cleanupUploadedFile(filePath);
        throw new Error(updateResult.error || 'فشل تحديث سجل السائق - Failed to update driver record');
      }

      return {
        success: true,
        imageUrl: publicUrl
      };

    } catch (error) {
      console.error('❌ License image upload error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'حدث خطأ غير معروف - Unknown error occurred'
      };
    }
  }

  /**
   * Fallback method to store image in database as base64 when storage upload fails
   * طريقة بديلة لتخزين الصورة في قاعدة البيانات كـ base64 عندما يفشل رفع التخزين
   */
  private async storeImageInDatabaseAsBase64(file: File, driverId: string): Promise<UploadResult> {
    try {
      console.log('🔄 Using fallback method: storing image as base64 in database');
      
      // Validate file size for base64 storage (max 1MB for base64)
      if (file.size > 1024 * 1024) {
        throw new Error('Image too large for database storage. Maximum size is 1MB for fallback method.');
      }
      
      // Convert file to base64
      const base64String = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(file);
      });

      // Store base64 string in a separate field in the drivers table
      // Note: This is not ideal for large images, but it's a fallback
      const { error } = await this.supabase
        .from('drivers')
        .update({ 
          license_image_url: base64String,
          // Add a flag to indicate this is base64 data
          license_image_storage_type: 'base64'
        })
        .eq('id', driverId);

      if (error) {
        throw new Error(`فشل في تخزين الصورة في قاعدة البيانات: ${error.message}`);
      }

      console.log('✅ Image stored in database as base64');
      
      return {
        success: true,
        imageUrl: base64String
      };
    } catch (error) {
      console.error('❌ Fallback method failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'فشل في الطريقة البديلة - Fallback method failed'
      };
    }
  }

  /**
   * Update driver record with new image URL
   * تحديث سجل السائق برابط الصورة الجديد
   */
  private async updateDriverImageUrl(driverId: string, imageUrl: string): Promise<UploadResult> {
    try {
      const { error } = await this.supabase
        .from('drivers')
        .update({ 
          license_image_url: imageUrl,
          license_image_storage_type: 'url'
        })
        .eq('id', driverId);

      if (error) {
        console.error(`❌ Failed to update license_image_url:`, error);
        throw new Error(`فشل تحديث صورة الرخصة - Failed to update license image`);
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Database update failed'
      };
    }
  }

  /**
   * Remove old image from storage before uploading new one
   * إزالة الصورة القديمة من التخزين قبل رفع صورة جديدة
   */
  private async removeOldImage(driverId: string): Promise<void> {
    try {
      // Get current image URL from database
      const { data: driver, error } = await this.supabase
        .from('drivers')
        .select('license_image_url, license_image_storage_type')
        .eq('id', driverId)
        .single();

      if (error || !driver) {
        return;
      }

      // Type-safe property access
      const imageUrl = (driver as DriverLicenseRecord).license_image_url;
      const storageType = (driver as DriverLicenseRecord).license_image_storage_type;

      if (!imageUrl) {
        return;
      }

      // If image is stored as base64 in database, clear it
      if (storageType === 'base64') {
        const { error: updateError } = await this.supabase
          .from('drivers')
          .update({ 
            license_image_url: null,
            license_image_storage_type: null
          })
          .eq('id', driverId);

        if (updateError) {
          console.warn(`⚠️ Could not clear base64 image from database: ${updateError.message}`);
        }
        return;
      }

      // Extract file path from URL for storage-based images
      try {
        // Validate imageUrl before creating URL object
        if (typeof imageUrl === 'string' && imageUrl.trim() !== '') {
          const url = new URL(imageUrl);
          
          // Extract the file path from the URL
          // The path will be something like: /storage/v1/object/public/Drivers-licenses/driver-id/filename
          // Or: /storage/v1/object/authenticated/Drivers-licenses/driver-id/filename
          const pathParts = url.pathname.split('/');
          const bucketIndex = pathParts.indexOf(this.BUCKET_NAME);
          
          if (bucketIndex !== -1 && bucketIndex < pathParts.length - 1) {
            // Extract everything after the bucket name
            const filePath = pathParts.slice(bucketIndex + 1).join('/');
            
            if (filePath) {
              // Check if bucket exists before attempting to remove
              const { data: buckets, error: listError } = await this.supabase.storage.listBuckets();
              const bucketExists = Array.isArray(buckets) && buckets.some((bucket: { name: string }) => bucket.name === this.BUCKET_NAME);
              
              if (bucketExists) {
                const { error: deleteError } = await this.supabase.storage
                  .from(this.BUCKET_NAME)
                  .remove([filePath]);

                if (deleteError) {
                  console.warn(`⚠️ Could not delete old image: ${deleteError.message}`);
                  // Handle PostgreSQL permission errors
                  if (deleteError.message.includes('42501') || deleteError.message.includes('must be owner')) {
                    console.log('ℹ️ Skipping image removal due to permissions - will be handled by storage policies');
                  }
                }
              } else {
                console.log(`ℹ️ Bucket ${this.BUCKET_NAME} does not exist, skipping image removal`);
              }
            }
          } else {
            // Try alternative method: check if URL contains the bucket name in a different format
            const bucketInPath = url.pathname.includes(`/${this.BUCKET_NAME}/`);
            
            if (bucketInPath) {
              // Extract path after bucket name
              const pathAfterBucket = url.pathname.split(`/${this.BUCKET_NAME}/`)[1];
              if (pathAfterBucket) {
                // Check if bucket exists before attempting to remove
                const { data: buckets, error: listError } = await this.supabase.storage.listBuckets();
                const bucketExists = Array.isArray(buckets) && buckets.some((bucket: { name: string }) => bucket.name === this.BUCKET_NAME);
                
                if (bucketExists) {
                  const { error: deleteError } = await this.supabase.storage
                    .from(this.BUCKET_NAME)
                    .remove([pathAfterBucket]);

                  if (deleteError) {
                    console.warn(`⚠️ Could not delete old image (alternative method): ${deleteError.message}`);
                    // Handle PostgreSQL permission errors
                    if (deleteError.message.includes('42501') || deleteError.message.includes('must be owner')) {
                      console.log('ℹ️ Skipping image removal due to permissions - will be handled by storage policies');
                    }
                  }
                } else {
                  console.log(`ℹ️ Bucket ${this.BUCKET_NAME} does not exist, skipping image removal (alternative method)`);
                }
              }
            }
          }
        }
      } catch (urlError) {
        console.warn(`⚠️ Invalid image URL format: ${imageUrl}`, urlError);
      }
    } catch (error) {
      console.warn(`⚠️ Error removing old image:`, error);
    }
  }

  /**
   * Clean up uploaded file if database update fails
   * تنظيف الملف المرفوع في حال فشل تحديث قاعدة البيانات
   */
  private async cleanupUploadedFile(filePath: string): Promise<void> {
    try {
      const { error } = await this.supabase.storage
        .from(this.BUCKET_NAME)
        .remove([filePath]);

      if (error) {
        console.warn(`⚠️ Failed to cleanup uploaded file: ${error.message}`);
      }
    } catch (error) {
      console.warn(`⚠️ Error during file cleanup:`, error);
    }
  }

  /**
   * Get current license image for a driver
   * الحصول على صورة الرخصة الحالية للسائق
   */
  async getDriverLicenseImage(driverId: string): Promise<{
    licenseImage?: string;
    storageType?: string;
    error?: string;
  }> {
    try {
      const { data: driver, error } = await this.supabase
        .from('drivers')
        .select('license_image_url, license_image_storage_type')
        .eq('id', driverId)
        .single();

      if (error) {
        throw new Error(`فشل في الحصول على صورة الرخصة - Failed to get license image: ${error.message}`);
      }

      // Type-safe access to driver license data
      const licenseData = driver as DriverLicenseRecord;

      return {
        licenseImage: licenseData?.license_image_url || undefined,
        storageType: licenseData?.license_image_storage_type || 'url'
      };
    } catch (error) {
      return {
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
}

// Create singleton instance for easy import
export const driverLicenseService = new DriverLicenseService();

// Utility functions for easy use
export async function uploadDriverLicenseImage(file: File, driverId: string): Promise<UploadResult> {
  return driverLicenseService.uploadLicenseImage(file, driverId);
}

export async function getDriverLicenseImage(driverId: string) {
  return driverLicenseService.getDriverLicenseImage(driverId);
}