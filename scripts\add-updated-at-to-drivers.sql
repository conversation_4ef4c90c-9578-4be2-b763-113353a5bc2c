-- Add updated_at column to drivers table
-- This column is required by the application for proper Supabase operations

ALTER TABLE public.drivers 
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Add a trigger to automatically update the updated_at column when a row is modified
-- First drop the trigger if it exists
DROP TRIGGER IF EXISTS update_drivers_updated_at ON public.drivers;

-- Then drop the function if it exists
DROP FUNCTION IF EXISTS public.update_updated_at_column();

-- Create the function
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for drivers table
CREATE TRIGGER update_drivers_updated_at 
    BEFORE UPDATE ON public.drivers 
    FOR EACH ROW 
    EXECUTE FUNCTION public.update_updated_at_column();

-- Add a comment to explain the column purpose
COMMENT ON COLUMN public.drivers.updated_at IS 'Timestamp of last update - تحديث آخر تعديل';

-- Update existing records to set the updated_at value to created_at or current time
UPDATE public.drivers 
SET updated_at = COALESCE(created_at, NOW()) 
WHERE updated_at IS NULL;