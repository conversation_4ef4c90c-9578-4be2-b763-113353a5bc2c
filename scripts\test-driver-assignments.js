const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: './.env.local' });

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials in .env.local');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testDriverAssignments() {
  console.log('Testing driver assignments query...');
  
  try {
    // First, get some vehicle IDs
    console.log('Fetching vehicle IDs...');
    const { data: vehicles, error: vehiclesError } = await supabase
      .from('vehicles')
      .select('id')
      .limit(2);
    
    if (vehiclesError) {
      console.error('Error fetching vehicles:', vehiclesError);
      return;
    }
    
    console.log('Vehicle IDs:', vehicles.map(v => v.id));
    
    if (vehicles && vehicles.length > 0) {
      const vehicleIds = vehicles.map(v => v.id);
      
      // Test the driver assignments query (the fixed version)
      console.log('Testing vehicle_assignments query...');
      const { data: assignmentsData, error: assignmentsError } = await supabase
        .from('vehicle_assignments')
        .select('vehicle_id, driver_id')
        .in('vehicle_id', vehicleIds)
        .is('end_date', null);
        
      if (assignmentsError) {
        console.error('Error fetching vehicle assignments:', assignmentsError);
      } else {
        console.log('Vehicle assignments query successful!');
        console.log('Assignments data:', assignmentsData);
        
        // If we have assignments, test fetching driver details
        if (assignmentsData && assignmentsData.length > 0) {
          const driverIds = assignmentsData.map(a => a.driver_id).filter(id => id);
          if (driverIds.length > 0) {
            console.log('Fetching driver details...');
            const { data: driversData, error: driversError } = await supabase
              .from('drivers')
              .select('id, license_expiry')
              .in('id', driverIds);
              
            if (driversError) {
              console.error('Error fetching drivers:', driversError);
            } else {
              console.log('Drivers query successful!');
              console.log('Drivers data:', driversData);
              
              // Test the mapping logic that was fixed
              console.log('Testing mapping logic...');
              const driverMap = new Map();
              driversData.forEach(driver => {
                driverMap.set(driver.id, driver);
              });
              
              const combinedData = assignmentsData.map(assignment => {
                const driver = driverMap.get(assignment.driver_id);
                return {
                  ...assignment,
                  driver: driver || null
                };
              });
              
              console.log('Combined data:', combinedData);
            }
          } else {
            console.log('No driver IDs to fetch');
          }
        } else {
          console.log('No vehicle assignments found');
        }
      }
    } else {
      console.log('No vehicles found');
    }
  } catch (err) {
    console.error('Error:', err);
  }
}

testDriverAssignments();