# Supabase Storage Setup for Driver License Images

This document explains how to set up Supabase Storage to allow users to upload, edit, and delete driver license images.

## Prerequisites

1. Access to your Supabase project dashboard
2. Administrative privileges to modify storage policies

## Steps to Configure Storage

### 1. Create the Storage Bucket

First, ensure the `Drivers-licenses` bucket exists:

1. Go to your Supabase project dashboard
2. Navigate to Storage → Buckets
3. Click "New Bucket"
4. Enter the following details:
   - Name: `Drivers-licenses`
   - Public: Yes (checked)
5. Click "Create Bucket"

Alternatively, you can run the SQL script [create-drivers-licenses-bucket.sql](../scripts/create-drivers-licenses-bucket.sql) in your Supabase SQL editor.

### 2. Configure Storage Policies

To allow any authenticated user to add, edit, or delete driver license images:

1. Go to your Supabase project dashboard
2. Navigate to Storage → Buckets
3. Click on the `Drivers-licenses` bucket
4. Go to the "Policies" tab
5. Create or update the policies with the following rules:

#### For SELECT (viewing images):
```sql
-- Allow anyone to view public images
SELECT USING (bucket_id = 'Drivers-licenses')
```

#### For INSERT (uploading images):
```sql
-- Allow any authenticated user to upload images
INSERT TO bucket_id = 'Drivers-licenses' AND (SELECT auth.role()) = 'authenticated'
```

#### For UPDATE (replacing images):
```sql
-- Allow any authenticated user to update images
UPDATE USING (bucket_id = 'Drivers-licenses' AND (SELECT auth.role()) = 'authenticated')
```

#### For DELETE (removing images):
```sql
-- Allow any authenticated user to delete images
DELETE USING (bucket_id = 'Drivers-licenses' AND (SELECT auth.role()) = 'authenticated')
```

Alternatively, you can run the SQL script [setup-drivers-licenses-storage-policies.sql](../scripts/setup-drivers-licenses-storage-policies.sql) in your Supabase SQL editor.

## Testing the Configuration

After setting up the policies:

1. Restart your development server
2. Try adding a new driver with a license image
3. Try editing an existing driver to update their license image
4. Verify that the images are properly uploaded to the `Drivers-licenses` bucket

## Security Considerations

The current configuration allows any authenticated user to:
- View all driver license images
- Upload new driver license images
- Replace existing driver license images
- Delete driver license images

This is suitable for development and testing but may not be appropriate for production environments. For production, consider implementing more restrictive policies based on user roles.

## Troubleshooting

### Common Issues

1. **"new row violates row-level security policy" Error**
   - This indicates that the storage policies are not properly configured
   - Double-check that all four policies (SELECT, INSERT, UPDATE, DELETE) are correctly set up

2. **"Bucket not found" Error**
   - Ensure the `Drivers-licenses` bucket exists
   - Verify the bucket name is spelled correctly

3. **Permission Denied Errors**
   - Make sure the bucket is set to "Public"
   - Verify that the policies reference the correct bucket name

### Verifying Policies

To check if your policies are correctly configured, run this query in your Supabase SQL editor:

```sql
SELECT * FROM storage.objects WHERE bucket_id = 'Drivers-licenses';
```

This should return any existing driver license images in the bucket.