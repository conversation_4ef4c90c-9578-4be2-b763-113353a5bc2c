"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON>alogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Eye, EyeOff } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { createClient } from "@/lib/supabase-browser"
import type { User } from "@/lib/supabase"
import bcrypt from "bcryptjs"

interface EditUserDialogProps {
  user: User
  language: "ar" | "en"
  open: boolean
  onClose: () => void
  onUserUpdated?: () => void
}

interface Branch {
  id: string
  name: string
}

export function EditUserDialog({ user, language, open, onClose, onUserUpdated }: EditUserDialogProps) {
  const [loading, setLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [changePassword, setChangePassword] = useState(false)
  const [branches, setBranches] = useState<Branch[]>([])
  const { toast } = useToast()

  const [formData, setFormData] = useState({
    full_name: "",
    email: "",
    password: "",
    confirmPassword: "",
    role: "viewer" as const,
    job_title: "",
    branch_id: "",
    user_status: "active" as const,
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  // Load user data when dialog opens
  useEffect(() => {
    if (open && user) {
      setFormData({
        full_name: user.full_name,
        email: user.email,
        password: "",
        confirmPassword: "",
        role: user.role as any,
        job_title: user.job_title || "",
        branch_id: user.branch_id || "",
        user_status: user.user_status as any,
      })
      setChangePassword(false)
      setErrors({})
    }
  }, [open, user])

  // Load branches on component mount
  useEffect(() => {
    async function loadBranches() {
      try {
        const supabase = createClient()
        const { data, error } = await supabase
          .from("branches")
          .select("id, name")
          .order("name")

        if (error) throw error
        setBranches(data || [])
      } catch (err) {
        console.error("Error loading branches:", err)
      }
    }

    loadBranches()
  }, [])

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    // Required fields
    if (!formData.full_name.trim()) {
      newErrors.full_name = language === "ar" ? "الاسم الكامل مطلوب" : "Full name is required"
    }

    if (!formData.email.trim()) {
      newErrors.email = language === "ar" ? "البريد الإلكتروني مطلوب" : "Email is required"
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = language === "ar" ? "البريد الإلكتروني غير صحيح" : "Invalid email format"
    }

    // Password validation only if changing password
    if (changePassword) {
      if (!formData.password) {
        newErrors.password = language === "ar" ? "كلمة المرور مطلوبة" : "Password is required"
      } else if (formData.password.length < 8) {
        newErrors.password = language === "ar" ? "كلمة المرور يجب أن تكون 8 أحرف على الأقل" : "Password must be at least 8 characters"
      } else if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(formData.password)) {
        newErrors.password = language === "ar" 
          ? "كلمة المرور يجب أن تحتوي على حرف كبير وصغير ورقم"
          : "Password must contain uppercase, lowercase, and number"
      }

      if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = language === "ar" ? "كلمة المرور غير متطابقة" : "Passwords do not match"
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setLoading(true)

    try {
      // Prepare update data
      const updateData: any = {
        full_name: formData.full_name.trim(),
        email: formData.email.toLowerCase().trim(),
        role: formData.role,
        job_title: formData.job_title.trim() || null,
        branch_id: formData.branch_id || null,
        user_status: formData.user_status,
        updated_at: new Date().toISOString(),
      }

      // Add password hash if changing password
      if (changePassword && formData.password) {
        const saltRounds = 12
        const hashedPassword = await bcrypt.hash(formData.password, saltRounds)
        updateData.password_hash = hashedPassword
      }

      // Update in Supabase
      const supabase = createClient()
      const { data, error } = await supabase
        .from("users")
        .update(updateData)
        .eq("id", user.id)
        .select()

      if (error) {
        // Handle specific error cases
        if (error.code === "23505" && error.message.includes("email")) {
          throw new Error(language === "ar" ? "البريد الإلكتروني مستخدم بالفعل" : "Email already exists")
        }
        throw error
      }

      // Show success message
      toast({
        title: language === "ar" ? "تم تحديث المستخدم" : "User Updated",
        description:
          language === "ar"
            ? `تم تحديث بيانات ${formData.full_name} بنجاح`
            : `${formData.full_name} has been updated successfully`,
      })

      // Trigger refresh and close dialog
      if (onUserUpdated) {
        onUserUpdated()
      }
      onClose()
    } catch (err) {
      console.error("Error updating user:", err)
      toast({
        title: language === "ar" ? "خطأ" : "Error",
        description: err instanceof Error ? err.message : (language === "ar" ? "حدث خطأ أثناء تحديث المستخدم" : "An error occurred while updating the user"),
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const roleOptions = [
    { value: "viewer", label: language === "ar" ? "مشاهد" : "Viewer" },
    { value: "operator", label: language === "ar" ? "مشغل" : "Operator" },
    { value: "mechanic", label: language === "ar" ? "ميكانيكي" : "Mechanic" },
    { value: "driver", label: language === "ar" ? "سائق" : "Driver" },
    { value: "branch_manager", label: language === "ar" ? "مدير فرع" : "Branch Manager" },
    { value: "fleet_manager", label: language === "ar" ? "مدير أسطول" : "Fleet Manager" },
    { value: "admin", label: language === "ar" ? "مدير" : "Admin" },
    { value: "system_super_admin", label: language === "ar" ? "مدير النظام الأعلى" : "System Super Admin" },
  ]

  const statusOptions = [
    { value: "active", label: language === "ar" ? "نشط" : "Active" },
    { value: "inactive", label: language === "ar" ? "غير نشط" : "Inactive" },
    { value: "suspended", label: language === "ar" ? "موقوف" : "Suspended" },
    { value: "pending", label: language === "ar" ? "في الانتظار" : "Pending" },
  ]

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{language === "ar" ? "تعديل المستخدم" : "Edit User"}</DialogTitle>
          <DialogDescription>
            {language === "ar" ? "تعديل تفاصيل وصلاحيات المستخدم" : "Edit user details and permissions"}
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            {/* Full Name */}
            <div className="space-y-2">
              <Label htmlFor="edit_full_name">{language === "ar" ? "الاسم الكامل" : "Full Name"} *</Label>
              <Input
                id="edit_full_name"
                value={formData.full_name}
                onChange={(e) => setFormData({ ...formData, full_name: e.target.value })}
                placeholder={language === "ar" ? "أحمد محمد علي" : "Ahmed Mohamed Ali"}
                className={errors.full_name ? "border-destructive" : ""}
              />
              {errors.full_name && <p className="text-sm text-destructive">{errors.full_name}</p>}
            </div>

            {/* Email */}
            <div className="space-y-2">
              <Label htmlFor="edit_email">{language === "ar" ? "البريد الإلكتروني" : "Email"} *</Label>
              <Input
                id="edit_email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                placeholder={language === "ar" ? "<EMAIL>" : "<EMAIL>"}
                className={errors.email ? "border-destructive" : ""}
              />
              {errors.email && <p className="text-sm text-destructive">{errors.email}</p>}
            </div>

            {/* Change Password Toggle */}
            <div className="flex items-center space-x-2">
              <Switch
                id="change-password"
                checked={changePassword}
                onCheckedChange={setChangePassword}
              />
              <Label htmlFor="change-password">
                {language === "ar" ? "تغيير كلمة المرور" : "Change Password"}
              </Label>
            </div>

            {/* Password Fields - Only shown when changing password */}
            {changePassword && (
              <>
                <div className="space-y-2">
                  <Label htmlFor="edit_password">{language === "ar" ? "كلمة المرور الجديدة" : "New Password"} *</Label>
                  <div className="relative">
                    <Input
                      id="edit_password"
                      type={showPassword ? "text" : "password"}
                      value={formData.password}
                      onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                      placeholder={language === "ar" ? "كلمة مرور قوية" : "Strong password"}
                      className={`pr-10 ${errors.password ? "border-destructive" : ""}`}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                  </div>
                  {errors.password && <p className="text-sm text-destructive">{errors.password}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="edit_confirmPassword">{language === "ar" ? "تأكيد كلمة المرور" : "Confirm Password"} *</Label>
                  <Input
                    id="edit_confirmPassword"
                    type="password"
                    value={formData.confirmPassword}
                    onChange={(e) => setFormData({ ...formData, confirmPassword: e.target.value })}
                    placeholder={language === "ar" ? "أعد إدخال كلمة المرور" : "Re-enter password"}
                    className={errors.confirmPassword ? "border-destructive" : ""}
                  />
                  {errors.confirmPassword && <p className="text-sm text-destructive">{errors.confirmPassword}</p>}
                </div>
              </>
            )}

            {/* Role and Status Grid */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit_role">{language === "ar" ? "الدور" : "Role"} *</Label>
                <Select value={formData.role} onValueChange={(value: any) => setFormData({ ...formData, role: value })}>
                  <SelectTrigger>
                    <SelectValue placeholder={language === "ar" ? "اختر الدور" : "Select role"} />
                  </SelectTrigger>
                  <SelectContent>
                    {roleOptions.map((role) => (
                      <SelectItem key={role.value} value={role.value}>
                        {role.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit_user_status">{language === "ar" ? "الحالة" : "Status"} *</Label>
                <Select value={formData.user_status} onValueChange={(value: any) => setFormData({ ...formData, user_status: value })}>
                  <SelectTrigger>
                    <SelectValue placeholder={language === "ar" ? "اختر الحالة" : "Select status"} />
                  </SelectTrigger>
                  <SelectContent>
                    {statusOptions.map((status) => (
                      <SelectItem key={status.value} value={status.value}>
                        {status.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Job Title */}
            <div className="space-y-2">
              <Label htmlFor="edit_job_title">{language === "ar" ? "المسمى الوظيفي" : "Job Title"}</Label>
              <Input
                id="edit_job_title"
                value={formData.job_title}
                onChange={(e) => setFormData({ ...formData, job_title: e.target.value })}
                placeholder={language === "ar" ? "مدير العمليات" : "Operations Manager"}
              />
            </div>

            {/* Branch */}
            <div className="space-y-2">
              <Label htmlFor="edit_branch_id">{language === "ar" ? "الفرع" : "Branch"}</Label>
              <Select value={formData.branch_id || "no-branch"} onValueChange={(value) => setFormData({ ...formData, branch_id: value === "no-branch" ? "" : value })}>
                <SelectTrigger>
                  <SelectValue placeholder={language === "ar" ? "اختر الفرع (اختياري)" : "Select branch (optional)"} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="no-branch">{language === "ar" ? "بدون فرع" : "No branch"}</SelectItem>
                  {branches.map((branch) => (
                    <SelectItem key={branch.id} value={branch.id}>
                      {branch.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* User Info */}
            <div className="bg-muted p-4 rounded-lg">
              <h4 className="font-medium mb-2">{language === "ar" ? "معلومات إضافية" : "Additional Information"}</h4>
              <div className="text-sm text-muted-foreground space-y-1">
                <p><strong>{language === "ar" ? "تاريخ الإنشاء:" : "Created:"}</strong> {new Date(user.created_at).toLocaleDateString()}</p>
                <p><strong>{language === "ar" ? "آخر تحديث:" : "Last Updated:"}</strong> {new Date(user.updated_at).toLocaleDateString()}</p>
                {user.last_login && (
                  <p><strong>{language === "ar" ? "آخر دخول:" : "Last Login:"}</strong> {new Date(user.last_login).toLocaleDateString()}</p>
                )}
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              {language === "ar" ? "إلغاء" : "Cancel"}
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (language === "ar" ? "جاري التحديث..." : "Updating...") : language === "ar" ? "تحديث" : "Update"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}