# Driver License Storage Setup

This document explains how to properly set up storage for driver license images in your Supabase project.

## Issue Description

The application attempts to automatically create a storage bucket named `Drivers-licenses` when uploading driver license images. However, this operation may fail with the error:

```
new row violates row-level security policy
```

This happens because regular authenticated users don't have permissions to create storage buckets in Supabase due to row-level security (RLS) policies.

## Solution

To fix this issue, you need to manually create the storage bucket with appropriate policies using the Supabase SQL editor.

## Steps to Set Up

1. **Access Supabase SQL Editor**:
   - Log in to your Supabase project dashboard
   - Navigate to the SQL Editor section

2. **Run the Setup Script**:
   - Copy and paste the following SQL script into the editor:
   
```sql
-- Create the Drivers-licenses bucket if it doesn't exist
INSERT INTO storage.buckets (id, name, public) 
VALUES ('Drivers-licenses', 'Drivers-licenses', true)
ON CONFLICT (id) DO UPDATE SET public = true;

-- Remove any existing policies on the Drivers-licenses bucket
DROP POLICY IF EXISTS "Allow read access to drivers licenses" ON storage.objects;
DROP POLICY IF EXISTS "Allow insert access to drivers licenses" ON storage.objects;
DROP POLICY IF EXISTS "Allow update access to drivers licenses" ON storage.objects;
DROP POLICY IF EXISTS "Allow delete access to drivers licenses" ON storage.objects;

-- Create new policies to allow any authenticated user to perform all operations
CREATE POLICY "Allow read access to drivers licenses"
ON storage.objects FOR SELECT
TO authenticated
USING ( bucket_id = 'Drivers-licenses' );

CREATE POLICY "Allow insert access to drivers licenses"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK ( bucket_id = 'Drivers-licenses' );

CREATE POLICY "Allow update access to drivers licenses"
ON storage.objects FOR UPDATE
TO authenticated
USING ( bucket_id = 'Drivers-licenses' );

CREATE POLICY "Allow delete access to drivers licenses"
ON storage.objects FOR DELETE
TO authenticated
USING ( bucket_id = 'Drivers-licenses' );

-- Grant necessary permissions to the storage.objects table
GRANT ALL ON storage.objects TO authenticated;

-- Verify the bucket was created
SELECT * FROM storage.buckets WHERE id = 'Drivers-licenses';
```

3. **Execute the Script**:
   - Click "Run" to execute the script
   - You should see the bucket information in the results panel

## What This Script Does

1. **Creates the Bucket**: Ensures the `Drivers-licenses` bucket exists and is public
2. **Removes Old Policies**: Clears any existing policies that might conflict
3. **Sets New Policies**: Creates permissive policies allowing authenticated users to:
   - Read (view) images
   - Insert (upload) images
   - Update (replace) images
   - Delete (remove) images
4. **Grants Permissions**: Ensures authenticated users have the necessary permissions

## Verification

After running the script, you can verify the setup by:

1. Checking that the bucket appears in the Storage section of your Supabase dashboard
2. Testing the driver license upload functionality in your application

## Troubleshooting

If you still encounter issues:

1. **Check Bucket Existence**:
   ```sql
   SELECT * FROM storage.buckets WHERE id = 'Drivers-licenses';
   ```

2. **Check Policies**:
   ```sql
   SELECT * FROM pg policies WHERE tablename = 'objects';
   ```

3. **Verify Permissions**:
   Ensure your Supabase project's authentication settings allow the operations you need.

## Security Note

The policies in this setup allow any authenticated user to perform all operations on driver license images. In a production environment, you might want to implement more restrictive policies based on user roles or ownership.