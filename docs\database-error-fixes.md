# Database Error Fixes

This document outlines the database errors encountered and their fixes.

## Issues Identified

### 1. Infinite Recursion in RLS Policy

**Error:** `Database error: infinite recursion detected in policy for relation "users"`

**Root Cause:** The RLS policy on the `rate_limit_logs` table contained a subquery that referenced the `users` table:

```sql
CREATE POLICY "Admin can view rate limit logs" ON rate_limit_logs
  FOR ALL TO authenticated
  USING (auth.role() = 'admin' OR auth.uid() = (SELECT id FROM users WHERE role = 'admin'));
```

The problematic part was `(SELECT id FROM users WHERE role = 'admin')` which created a circular dependency when the `users` table had its own RLS policies.

**Fix Applied:**
- Updated the policy to avoid the subquery
- Created a new policy that only allows users to see their own rate limit logs
- Added a script `scripts/fix-infinite-recursion-policy.sql` to fix existing databases

### 2. UserManagement Component Null Reference Error

**Error:** `TypeError: Cannot read properties of undefined (reading 'filter')`

**Root Cause:** The `users` data from the `useUsers` hook could be `undefined` in certain error conditions, but the component was trying to call `.filter()` on it without checking.

**Fix Applied:**
- Added null check for `users` data before filtering
- Added optional chaining (`?.`) for user properties in the filter function
- Return loading spinner if users data is not available

### 3. Poor Error Handling in fetchCurrentUserProfile

**Error:** Generic database errors without specific handling

**Root Cause:** The error handling was too generic and didn't provide meaningful feedback for specific database issues.

**Fix Applied:**
- Added specific error handling for infinite recursion errors
- Added handling for RLS policy violations
- Improved logging to help with debugging
- Provided user-friendly error messages

## Files Modified

1. **scripts/rate-limiting-policies.sql**
   - Fixed the problematic RLS policy to avoid infinite recursion

2. **scripts/fix-infinite-recursion-policy.sql** (NEW)
   - Script to fix existing databases with the problematic policy

3. **components/user-management.tsx**
   - Added null checks for users data
   - Added optional chaining for user properties

4. **hooks/use-fleet-data.ts**
   - Improved error handling in fetchCurrentUserProfile
   - Added specific error messages for different error types
   - Enhanced logging for debugging

## How to Apply Fixes

### For New Deployments
The fixes are already included in the updated files. Just deploy normally.

### For Existing Databases
Run the following SQL script in your Supabase SQL Editor:

```sql
-- Fix infinite recursion in rate_limit_logs RLS policy
DROP POLICY IF EXISTS "Admin can view rate limit logs" ON rate_limit_logs;

CREATE POLICY "Users can view their own rate limit logs" ON rate_limit_logs
  FOR ALL TO authenticated
  USING (auth.uid() = user_id);
```

Or run the complete script: `scripts/fix-infinite-recursion-policy.sql`

## Prevention

To prevent similar issues in the future:

1. **Avoid Subqueries in RLS Policies:** Don't use subqueries that reference other tables with RLS enabled
2. **Test RLS Policies:** Always test policies with different user roles and scenarios
3. **Use Service Role for Admin Operations:** For admin-only operations, use the service role key instead of complex RLS policies
4. **Add Proper Null Checks:** Always check for null/undefined data before using array methods
5. **Implement Specific Error Handling:** Handle different types of database errors with appropriate user messages

## Monitoring

Monitor your Supabase logs for:
- Infinite recursion errors
- RLS policy violations
- Performance issues with complex policies

Use the `rate_limit_logs` table to track unusual patterns in database access.
