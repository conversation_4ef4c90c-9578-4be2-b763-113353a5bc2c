import { createBrowserClient } from '@supabase/ssr'

// No singleton caching - create new client each time for runtime env var support
let isCreatingClient = false

// Store client reference for potential reset
let supabaseClient: ReturnType<typeof createBrowserClient> | null = null

export function createClient() {
  // Always create a new client at runtime to ensure proper env vars are used
  // Set the creating flag
  isCreatingClient = true

  try {
    // Always use environment variables, with fallbacks only if truly undefined
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://tilywrugwehpbeljwmsx.supabase.co';
    const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRpbHl3cnVnd2VocGJlbGp3bXN4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTU3MDUwNTUsImV4cCI6MjA3MTI4MTA1NX0.N_GGEnW5tH0E8lhSkCktwtjzWbbhrNO7kryO5c04wN0';

    // Create client with stable configuration
    supabaseClient = createBrowserClient(supabaseUrl, supabaseKey, {
      auth: {
        // Enable automatic token refresh but with controlled behavior
        autoRefreshToken: true,
        // Keep session persistence for better UX
        persistSession: true,
        // Enable session detection in URL for proper auth flow
        detectSessionInUrl: true,
        // Use PKCE flow for better security
        flowType: 'pkce',
      },
      global: {
        headers: {
          'x-application-name': 'fleet-management-dashboard',
        },
      },
    })

    return supabaseClient
  } finally {
    isCreatingClient = false
  }
}

// Function to reset the Supabase client (useful for schema cache issues)
export function resetClient() {
  // Clear the client reference to force creation of a new one
  supabaseClient = null
}