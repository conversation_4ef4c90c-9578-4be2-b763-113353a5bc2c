-- SAFE FIX: Handles existing policies gracefully
-- Run this complete script in Supabase SQL Editor

-- Step 1: Disable <PERSON><PERSON> temporarily to stop recursion
ALTER TABLE users DISABLE ROW LEVEL SECURITY;

-- Step 2: Drop ALL possible existing policies (more comprehensive list)
DROP POLICY IF EXISTS "Users can access their own data" ON users;
DROP POLICY IF EXISTS "Users can access own data" ON users;
DROP POLICY IF EXISTS "Users can access own record" ON users;
DROP POLICY IF EXISTS "Branch managers can access branch users" ON users;
DROP POLICY IF EXISTS "Admins can access all users" ON users;
DROP POLICY IF EXISTS "Users can view users in same branch" ON users;
DROP POLICY IF EXISTS "Enable read access for all users" ON users;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON users;
DROP POLICY IF EXISTS "Enable update for users based on email" ON users;
DROP POLICY IF EXISTS "Enable delete for users based on email" ON users;
DROP POLICY IF EXISTS "Service role can access all users" ON users;
DROP POLICY IF EXISTS "Service role full access" ON users;
DROP POLICY IF EXISTS "Allow users to view own profile" ON users;
DROP POLICY IF EXISTS "Allow users to update own profile" ON users;

-- Step 3: Re-enable RLS
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Step 4: Create new policies with unique names
CREATE POLICY "user_own_access_policy_2024" ON users
  FOR ALL TO authenticated
  USING (auth.uid() = id);

CREATE POLICY "service_role_full_access_2024" ON users
  FOR ALL TO service_role
  USING (true);

-- Step 5: Fix other tables - Branches
ALTER TABLE branches DISABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Branch access based on user branch" ON branches;
DROP POLICY IF EXISTS "All authenticated users can read branches" ON branches;
DROP POLICY IF EXISTS "Users can access their branch" ON branches;
ALTER TABLE branches ENABLE ROW LEVEL SECURITY;
CREATE POLICY "branches_read_access_2024" ON branches
  FOR SELECT TO authenticated
  USING (true);

-- Step 6: Fix other tables - Vehicles
ALTER TABLE vehicles DISABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Vehicle access based on user branch" ON vehicles;
DROP POLICY IF EXISTS "All authenticated users can read vehicles" ON vehicles;
DROP POLICY IF EXISTS "Users can access vehicles in their branch" ON vehicles;
ALTER TABLE vehicles ENABLE ROW LEVEL SECURITY;
CREATE POLICY "vehicles_read_access_2024" ON vehicles
  FOR SELECT TO authenticated
  USING (true);

-- Step 7: Fix other tables - Drivers
ALTER TABLE drivers DISABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Driver access based on user branch" ON drivers;
DROP POLICY IF EXISTS "All authenticated users can read drivers" ON drivers;
DROP POLICY IF EXISTS "Users can access drivers in their branch" ON drivers;
ALTER TABLE drivers ENABLE ROW LEVEL SECURITY;
CREATE POLICY "drivers_read_access_2024" ON drivers
  FOR SELECT TO authenticated
  USING (true);

-- Step 8: Test that the fix worked
SELECT 'Policies updated successfully! Infinite recursion should be fixed.' as status;

-- Step 9: Verify you can query users table
SELECT COUNT(*) as user_count FROM users;
