"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { <PERSON><PERSON>, AlertTriangle, CheckCircle, Clock } from "lucide-react"
import { useUpcomingServices, useMaintenanceStatistics } from "@/hooks/use-fleet-data"
import { LoadingSpinner } from "@/components/loading-spinner"
import { ErrorDisplay } from "@/components/error-display"
import { AddMaintenanceDialog } from "@/components/forms/add-maintenance-dialog"

interface MaintenanceProps {
  language: "ar" | "en"
}

export function Maintenance({ language }: MaintenanceProps) {
  const { data: services, loading: servicesLoading, error: servicesError } = useUpcomingServices(50)
  const { data: stats, loading: statsLoading, error: statsError } = useMaintenanceStatistics()

  if (servicesLoading || statsLoading) {
    return <LoadingSpinner className="h-64" />
  }

  if (servicesError || statsError) {
    return <ErrorDisplay error={servicesError || statsError || "Unknown error"} />
  }

  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      overdue: { variant: "destructive" as const, label: language === "ar" ? "متأخر" : "Overdue", icon: AlertTriangle },
      urgent: { variant: "default" as const, label: language === "ar" ? "عاجل" : "Urgent", icon: Clock },
      due_soon: { variant: "secondary" as const, label: language === "ar" ? "قريباً" : "Due Soon", icon: Clock },
      scheduled: { variant: "outline" as const, label: language === "ar" ? "مجدول" : "Scheduled", icon: CheckCircle },
    }

    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.scheduled
    const Icon = config.icon

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>
    )
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(language === "ar" ? "ar-EG" : "en-EG", {
      style: "currency",
      currency: "EGP",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">{language === "ar" ? "الصيانة" : "Maintenance"}</h2>
          <p className="text-muted-foreground">
            {language === "ar" ? "إدارة وجدولة صيانة المركبات" : "Manage and schedule vehicle maintenance"}
          </p>
        </div>
        <AddMaintenanceDialog language={language} />
      </div>

      {/* Statistics Cards */}
      {stats && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === "ar" ? "إجمالي السجلات" : "Total Records"}
              </CardTitle>
              <Wrench className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total_records.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                {language === "ar" ? "سجلات الصيانة" : "Maintenance records"}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === "ar" ? "التكلفة السنوية" : "YTD Cost"}
              </CardTitle>
              <Wrench className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(stats.total_cost_ytd)}</div>
              <p className="text-xs text-muted-foreground">
                {language === "ar" ? "إجمالي التكلفة" : "Total maintenance cost"}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === "ar" ? "متوسط التكلفة" : "Average Cost"}
              </CardTitle>
              <Wrench className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(stats.average_cost)}</div>
              <p className="text-xs text-muted-foreground">{language === "ar" ? "لكل خدمة" : "Per service"}</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === "ar" ? "الخدمات المستحقة" : "Due Services"}
              </CardTitle>
              <AlertTriangle className="h-4 w-4 text-destructive" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-destructive">{stats.next_service_due}</div>
              <p className="text-xs text-muted-foreground">{language === "ar" ? "تحتاج اهتمام" : "Need attention"}</p>
            </CardContent>
          </Card>
        </div>
      )}

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wrench className="h-5 w-5" />
            {language === "ar" ? "الخدمات القادمة" : "Upcoming Services"}
          </CardTitle>
          <CardDescription>
            {language === "ar" ? "الصيانة والخدمات المجدولة" : "Scheduled maintenance and services"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{language === "ar" ? "المركبة" : "Vehicle"}</TableHead>
                <TableHead>{language === "ar" ? "نوع الخدمة" : "Service Type"}</TableHead>
                <TableHead>{language === "ar" ? "الأولوية" : "Priority"}</TableHead>
                <TableHead>{language === "ar" ? "موعد الاستحقاق" : "Due Date"}</TableHead>
                <TableHead>{language === "ar" ? "الكيلومترات المستحقة" : "Due KM"}</TableHead>
                <TableHead>{language === "ar" ? "الأيام المتبقية" : "Days Left"}</TableHead>
                <TableHead>{language === "ar" ? "الإجراءات" : "Actions"}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {Array.isArray(services) && services.length > 0 ? (
                services.map((service) => {
                  // Add safety check for service object
                  if (!service) {
                    console.warn('Encountered null/undefined service object');
                    return null;
                  }
                  
                  return (
                    <TableRow key={`${service.vehicle_id}-${service.service_type}`}>
                      <TableCell className="font-medium">{service.plate_number || '-'}</TableCell>
                      <TableCell>{service.service_type || '-'}</TableCell>
                      <TableCell>{getPriorityBadge(service.priority)}</TableCell>
                      <TableCell>
                        {service.due_date
                          ? (() => {
                              try {
                                // More robust date parsing
                                const date = new Date(service.due_date);
                                // Check if the date is valid
                                if (isNaN(date.getTime())) {
                                  console.warn('Invalid date in maintenance component:', service.due_date);
                                  return "-";
                                }
                                return date.toLocaleDateString(language === "ar" ? "ar-EG" : "en-EG");
                              } catch (err) {
                                console.warn('Error parsing date in maintenance component:', service.due_date, err);
                                return "-";
                              }
                            })()
                          : "-"}
                      </TableCell>
                      <TableCell>{service.due_km?.toLocaleString() || "-"}</TableCell>
                      <TableCell>
                        {service.days_until_due !== null && service.days_until_due !== undefined
                          ? `${service.days_until_due} ${language === "ar" ? "يوم" : "days"}`
                          : "-"}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button variant="ghost" size="sm">
                            {language === "ar" ? "جدولة" : "Schedule"}
                          </Button>
                          <Button variant="ghost" size="sm">
                            {language === "ar" ? "عرض" : "View"}
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  )
                })
              ) : (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    {servicesError ? (
                      <div className="text-destructive">{servicesError}</div>
                    ) : (
                      <div className="text-muted-foreground">
                        {language === "ar" ? "لا توجد خدمات قادمة" : "No upcoming services"}
                      </div>
                    )}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
