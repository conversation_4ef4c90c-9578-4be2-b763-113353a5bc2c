"use client"

import React, { useState, useEffect, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Upload, X } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { createClient, resetClient } from "@/lib/supabase-browser"
import { uploadDriverLicenseImage, getDriverLicenseImage } from "@/lib/driver-license-service"
import type { Driver } from "@/lib/supabase"

interface EditDriverDialogProps {
  driver: Driver
  language: "ar" | "en"
  open: boolean
  onClose: () => void
  onDriverUpdated?: () => void
}

export function EditDriverDialog({ driver, language, open, onClose, onDriverUpdated }: EditDriverDialogProps) {
  // Add protection for null driver
  if (!driver) {
    return null;
  }

  const [loading, setLoading] = useState(false)
  const { toast } = useToast()
  const supabaseClient = createClient()
  
  // File input refs
  const licenseImageRef = useRef<HTMLInputElement>(null)
  
  // License image states
  const [licenseImage, setLicenseImage] = useState<File | null>(null)
  const [licenseImagePreview, setLicenseImagePreview] = useState<string | null>(null)
  const [currentLicenseImage, setCurrentLicenseImage] = useState<string | null>(null)
  const [shouldRemoveLicenseImage, setShouldRemoveLicenseImage] = useState(false)

  // Form state with all driver fields
  // Note: tourism_permit and airport_permit are optional fields
  const [formData, setFormData] = useState({
    full_name: "",
    national_id: "",
    license_number: "",
    license_expiry: "",
    phone: "",
    emergency_phone: "",
    tourism_permit: false,
    airport_permit: false,
    branch_id: "",
  })

  const [branches, setBranches] = useState<Array<{ id: string; name: string }>>([])

  // Load driver data when dialog opens
  useEffect(() => {
    if (open && driver) {
      setFormData({
        full_name: driver.full_name || "",
        national_id: driver.national_id || "",
        license_number: driver.license_number || "",
        license_expiry: driver.license_expiry || "",
        phone: driver.phone || "",
        emergency_phone: driver.emergency_phone || "",
        tourism_permit: driver.tourism_permit || false,
        airport_permit: driver.airport_permit || false,
        branch_id: driver.branch_id || "",
      })
      
      // Set current license image if exists
      if (driver.license_image_url) {
        setCurrentLicenseImage(driver.license_image_url)
      } else {
        // Try to fetch the license image from the service
        loadExistingLicenseImage(driver.id)
      }
    }
  }, [open, driver])
  
  // Load existing license image from database
  const loadExistingLicenseImage = async (driverId: string) => {
    try {
      const imageData = await getDriverLicenseImage(driverId)
      
      if (!imageData.error && imageData.licenseImage) {
        // Check if it's a base64 image or a URL
        if (imageData.storageType === 'base64' || imageData.licenseImage.startsWith('data:image')) {
          // It's a base64 image, use it directly
          setCurrentLicenseImage(imageData.licenseImage)
        } else {
          // It's a URL, use it as is
          setCurrentLicenseImage(imageData.licenseImage)
        }
      }
    } catch (error) {
      console.warn('Failed to load existing license image:', error)
    }
  }

  // Handle license image selection
  const handleLicenseImageChange = (file: File | null) => {
    if (file) {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
      if (!allowedTypes.includes(file.type)) {
        toast({
          title: language === "ar" ? "نوع ملف غير مدعوم" : "Unsupported file type",
          description: language === "ar" ? "يرجى استخدام JPEG, PNG أو WebP" : "Please use JPEG, PNG or WebP",
          variant: "destructive"
        })
        return
      }
      
      // Validate file size (5MB max)
      if (file.size > 5 * 1024 * 1024) {
        toast({
          title: language === "ar" ? "حجم الملف كبير" : "File too large",
          description: language === "ar" ? "الحد الأقصى 5 ميجابايت" : "Maximum size is 5MB",
          variant: "destructive"
        })
        return
      }
      
      // Update license image state
      setLicenseImage(file)
      
      // Create preview URL
      const previewUrl = URL.createObjectURL(file)
      setLicenseImagePreview(previewUrl)
    } else {
      // Remove image
      setLicenseImage(null)
      setLicenseImagePreview(null)
      
      // Reset file input
      if (licenseImageRef.current) {
        licenseImageRef.current.value = ''
      }
    }
  }
  
  // Remove license image
  const removeLicenseImage = () => {
    handleLicenseImageChange(null)
    setShouldRemoveLicenseImage(true)
  }

  // Fetch branches
  useEffect(() => {
    if (open) {
      fetchBranches()
    }
  }, [open])

  const fetchBranches = async () => {
    try {
      const { data, error } = await supabaseClient
        .from('branches')
        .select('id, name')
        .order('name')
      
      if (error) {
        console.error('Error fetching branches:', error.message)
      } else {
        setBranches(data || [])
      }
    } catch (error) {
      console.error('Error fetching branches:', error)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      // Basic validation
      if (!formData.full_name.trim()) {
        throw new Error(language === "ar" ? "الاسم الكامل مطلوب" : "Full name is required")
      }

      if (!formData.license_number.trim()) {
        throw new Error(language === "ar" ? "رقم الرخصة مطلوب" : "License number is required")
      }

      if (!formData.license_expiry.trim()) {
        throw new Error(language === "ar" ? "تاريخ انتهاء الرخصة مطلوب" : "License expiry date is required")
      }

      if (!formData.phone.trim()) {
        throw new Error(language === "ar" ? "رقم الهاتف مطلوب" : "Phone number is required")
      }

      // Prepare update data - ONLY include fields that have values
      // This avoids schema cache issues with optional columns
      const updateData: Record<string, any> = {
        full_name: formData.full_name.trim(),
        license_number: formData.license_number.trim(),
        license_expiry: formData.license_expiry.trim(),
        phone: formData.phone.trim(),
        updated_at: new Date().toISOString(),
      }

      // Add optional fields only if they have values
      if (formData.national_id?.trim()) {
        updateData.national_id = formData.national_id.trim()
      }
      
      if (formData.emergency_phone?.trim()) {
        updateData.emergency_phone = formData.emergency_phone.trim()
      }
      
      // Only add permit fields if they are explicitly set to true or false
      // This avoids issues with schema cache not recognizing the columns
      if (formData.tourism_permit === true || formData.tourism_permit === false) {
        updateData.tourism_permit = formData.tourism_permit
      }
      
      if (formData.airport_permit === true || formData.airport_permit === false) {
        updateData.airport_permit = formData.airport_permit
      }
      
      // Add branch_id if it has a value
      if (formData.branch_id) {
        updateData.branch_id = formData.branch_id
      } else {
        // Explicitly set to null if empty to avoid schema cache issues
        updateData.branch_id = null
      }

      // Try to update in Supabase
      let { data, error } = await supabaseClient
        .from("drivers")
        .update(updateData)
        .eq("id", driver.id)
        .select(
          "id, full_name, national_id, license_number, license_expiry, phone, emergency_phone, tourism_permit, airport_permit, performance_score, branch_id, license_image_url, license_image_storage_type, created_at, last_performance_update"
        )

      if (error) {
        console.warn("Supabase update failed:", error)
        // If we get a schema cache error, try to reset the client and retry
        if (error.message.includes('schema cache') || error.message.includes('column') && error.message.includes('drivers')) {
          console.warn('Schema cache error detected during update, resetting client and retrying...')
          // Reset the Supabase client to clear the schema cache
          resetClient()
          
          // Create a small delay to ensure the reset is processed
          await new Promise(resolve => setTimeout(resolve, 100))
          
          // Get a new client instance
          const newSupabaseClient = createClient()
          
          // Retry the update
          const { data: retryData, error: retryError } = await newSupabaseClient
            .from("drivers")
            .update(updateData)
            .eq("id", driver.id)
            .select()
          
          if (retryError) {
            throw new Error(`Update error after client reset: ${retryError.message}`)
          }
          
          data = retryData
        } else {
          throw new Error(`Update error: ${error.message}`)
        }
      }

      if (!data || data.length === 0) {
        throw new Error(language === "ar" ? "فشل تحديث البيانات" : "Failed to update data")
      }

      // Handle license image removal
      if (shouldRemoveLicenseImage && !licenseImage) {
        console.log('🗑️ Removing driver license image...')
        
        // Remove the image from storage and database
        const { error: removeError } = await supabaseClient
          .from('drivers')
          .update({ 
            license_image_url: null,
            license_image_storage_type: null
          })
          .eq('id', driver.id)
        
        if (removeError) {
          console.warn('⚠️ Failed to remove license image from database:', removeError)
        } else {
          console.log('✅ License image removed successfully')
        }
        
        // Also try to remove from storage if it was stored there
        try {
          // Get current image URL to extract file path
          const { data: driverData, error: fetchError } = await supabaseClient
            .from('drivers')
            .select('license_image_url')
            .eq('id', driver.id)
            .single()
          
          if (!fetchError && driverData?.license_image_url) {
            const imageUrl = driverData.license_image_url
            // Extract file path from URL
            if (typeof imageUrl === 'string' && imageUrl.trim() !== '') {
              const url = new URL(imageUrl)
              const pathParts = url.pathname.split('/')
              const bucketIndex = pathParts.indexOf('Drivers-licenses')
              
              if (bucketIndex !== -1 && bucketIndex < pathParts.length - 1) {
                const filePath = pathParts.slice(bucketIndex).join('/')
                if (filePath) {
                  const { error: deleteError } = await supabaseClient.storage
                    .from('Drivers-licenses')
                    .remove([filePath])
                  
                  if (deleteError) {
                    console.warn('⚠️ Failed to remove license image from storage:', deleteError)
                  } else {
                    console.log('✅ License image removed from storage successfully')
                  }
                }
              }
            }
          }
        } catch (urlError) {
          console.warn('⚠️ Error processing image removal:', urlError)
        }
      }
      // Handle license image upload if a new image was provided
      else if (licenseImage) {
        console.log('📸 Starting license image upload for driver update...')
        
        const uploadResult = await uploadDriverLicenseImage(licenseImage, driver.id)
        
        if (!uploadResult.success) {
          console.warn('⚠️ License image failed to upload:', uploadResult.error)
          
          toast({
            title: language === "ar" ? "تم تحديث السائق مع تحذير" : "Driver Updated with Warning",
            description: 
              language === "ar"
                ? `تم تحديث السائق ${formData.full_name} لكن فشل رفع صورة الرخصة: ${uploadResult.error}`
                : `Driver ${formData.full_name} updated but license image failed to upload: ${uploadResult.error}`,
            variant: "default"
          })
        } else {
          console.log('✅ License image uploaded successfully')
        }
      }

      toast({
        title: language === "ar" ? "تم تحديث السائق" : "Driver Updated",
        description:
          language === "ar"
            ? `تم تحديث السائق ${formData.full_name} بنجاح`
            : `Driver ${formData.full_name} has been updated successfully`,
      })

      // Clean up preview URL
      if (licenseImagePreview) {
        URL.revokeObjectURL(licenseImagePreview)
      }
      setLicenseImage(null)
      setLicenseImagePreview(null)
      setShouldRemoveLicenseImage(false)
      
      // Close dialog and trigger refresh
      onClose()
      if (onDriverUpdated) {
        onDriverUpdated()
      }

    } catch (error: any) {
      console.error('Driver update error:', error.message)
      
      toast({
        title: language === "ar" ? "خطأ" : "Error",
        description: error.message || (language === "ar" ? "حدث خطأ أثناء تحديث السائق" : "An error occurred while updating the driver"),
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>{language === "ar" ? "تعديل السائق" : "Edit Driver"}</DialogTitle>
          <DialogDescription>
            {language === "ar" ? "تعديل تفاصيل السائق" : "Edit driver details"}
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="flex-1 overflow-y-auto">
          <div className="grid gap-4 py-4">
            {/* Full Name - Required */}
            <div className="space-y-2">
              <Label htmlFor="edit_full_name">{language === "ar" ? "الاسم الكامل" : "Full Name"} *</Label>
              <Input
                id="edit_full_name"
                value={formData.full_name}
                onChange={(e) => setFormData({ ...formData, full_name: e.target.value })}
                placeholder={language === "ar" ? "أحمد محمد علي" : "Ahmed Mohamed Ali"}
                required
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* National ID */}
              <div className="space-y-2">
                <Label htmlFor="edit_national_id">{language === "ar" ? "رقم الهوية" : "National ID"}</Label>
                <Input
                  id="edit_national_id"
                  value={formData.national_id}
                  onChange={(e) => setFormData({ ...formData, national_id: e.target.value })}
                  placeholder="12345678901234"
                />
              </div>

              {/* License Number - Required */}
              <div className="space-y-2">
                <Label htmlFor="edit_license_number">{language === "ar" ? "رقم الرخصة" : "License Number"} *</Label>
                <Input
                  id="edit_license_number"
                  value={formData.license_number}
                  onChange={(e) => setFormData({ ...formData, license_number: e.target.value })}
                  placeholder="DL123456"
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Phone - Required */}
              <div className="space-y-2">
                <Label htmlFor="edit_phone">{language === "ar" ? "رقم الهاتف" : "Phone Number"} *</Label>
                <Input
                  id="edit_phone"
                  value={formData.phone}
                  onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                  placeholder="+966501234567"
                  required
                />
              </div>

              {/* Emergency Phone */}
              <div className="space-y-2">
                <Label htmlFor="edit_emergency_phone">{language === "ar" ? "رقم الطوارئ" : "Emergency Phone"}</Label>
                <Input
                  id="edit_emergency_phone"
                  value={formData.emergency_phone}
                  onChange={(e) => setFormData({ ...formData, emergency_phone: e.target.value })}
                  placeholder="+966501234567"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* License Expiry - Required */}
              <div className="space-y-2">
                <Label htmlFor="edit_license_expiry">{language === "ar" ? "انتهاء الرخصة" : "License Expiry"} *</Label>
                <Input
                  id="edit_license_expiry"
                  type="date"
                  value={formData.license_expiry}
                  onChange={(e) => setFormData({ ...formData, license_expiry: e.target.value })}
                  required
                />
              </div>
            </div>

            {/* Permits */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="tourism_permit"
                  checked={formData.tourism_permit}
                  onCheckedChange={(checked) => setFormData({ ...formData, tourism_permit: checked })}
                />
                <Label htmlFor="tourism_permit">{language === "ar" ? "تصريح سياحي" : "Tourism Permit"}</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Switch
                  id="airport_permit"
                  checked={formData.airport_permit}
                  onCheckedChange={(checked) => setFormData({ ...formData, airport_permit: checked })}
                />
                <Label htmlFor="airport_permit">{language === "ar" ? "تصريح مطار" : "Airport Permit"}</Label>
              </div>
            </div>

            {/* Branch */}
            <div className="space-y-2">
              <Label htmlFor="edit_branch_id">{language === "ar" ? "الفرع" : "Branch"}</Label>
              <Select
                value={formData.branch_id}
                onValueChange={(value) => setFormData({ ...formData, branch_id: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder={language === "ar" ? "اختر الفرع" : "Select branch"} />
                </SelectTrigger>
                <SelectContent>
                  {branches.map((branch) => (
                    <SelectItem key={branch.id} value={branch.id}>
                      {branch.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* License Image Section */}
            <div className="space-y-4">
              <div className="border-t pt-4">
                <h4 className="font-medium mb-4">{language === "ar" ? "صورة رخصة القيادة" : "Driver's License Image"}</h4>
                
                <div className="grid grid-cols-1 gap-6">
                  {/* License Image */}
                  <div className="space-y-3">
                    <Label>{language === "ar" ? "صورة رخصة القيادة" : "Driver's License"}</Label>
                    
                    {/* Current Image Display */}
                    {currentLicenseImage && !licenseImagePreview && (
                      <div className="relative">
                        <img 
                          src={currentLicenseImage} 
                          alt={language === "ar" ? "صورة رخصة القيادة الحالية" : "Current driver's license"}
                          className="w-full h-40 object-contain rounded-lg border"
                        />
                        <div className="absolute top-2 right-2">
                          <Button
                            type="button"
                            variant="destructive"
                            size="sm"
                            onClick={() => {
                              setCurrentLicenseImage(null)
                              setShouldRemoveLicenseImage(true)
                              if (licenseImageRef.current) {
                                licenseImageRef.current.click()
                              }
                            }}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                        <p className="text-xs text-muted-foreground mt-1">
                          {language === "ar" ? "الصورة الحالية" : "Current image"}
                        </p>
                      </div>
                    )}
                    
                    {/* New Image Preview */}
                    {licenseImagePreview && (
                      <div className="relative">
                        <img 
                          src={licenseImagePreview} 
                          alt={language === "ar" ? "معاينة صورة رخصة القيادة" : "Driver's license preview"}
                          className="w-full h-40 object-contain rounded-lg border"
                        />
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          className="absolute top-2 right-2"
                          onClick={removeLicenseImage}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                        <p className="text-xs text-green-600 mt-1">
                          {language === "ar" ? "صورة جديدة" : "New image"}
                        </p>
                      </div>
                    )}
                    
                    {/* Upload Interface */}
                    {!licenseImagePreview && (
                      <div 
                        className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center cursor-pointer hover:border-gray-400 transition-colors"
                        onClick={() => licenseImageRef.current?.click()}
                      >
                        <Upload className="mx-auto h-8 w-8 text-gray-400 mb-2" />
                        <p className="text-sm text-gray-600">
                          {language === "ar" ? "انقر لرفع صورة جديدة" : "Click to upload new image"}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          {language === "ar" ? "JPEG, PNG, WebP (حد أقصى 5MB)" : "JPEG, PNG, WebP (max 5MB)"}
                        </p>
                      </div>
                    )}
                    
                    <input
                      ref={licenseImageRef}
                      type="file"
                      accept="image/jpeg,image/jpg,image/png,image/webp"
                      className="hidden"
                      onChange={(e) => {
                        const file = e.target.files?.[0] || null
                        handleLicenseImageChange(file)
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Driver Info */}
            <div className="bg-muted p-4 rounded-lg">
              <h4 className="font-medium mb-2">{language === "ar" ? "معلومات إضافية" : "Additional Information"}</h4>
              <div className="text-sm text-muted-foreground space-y-1">
                <p><strong>{language === "ar" ? "تاريخ الإنشاء:" : "Created:"}</strong> {driver.created_at ? (() => {
                  try {
                    return new Date(driver.created_at).toLocaleDateString();
                  } catch (e) {
                    return 'Invalid Date';
                  }
                })() : 'N/A'}</p>
                {driver.last_performance_update && (
                  <p><strong>{language === "ar" ? "آخر تحديث للأداء:" : "Last Performance Update:"}</strong> {driver.last_performance_update ? (() => {
                    try {
                      return new Date(driver.last_performance_update).toLocaleDateString();
                    } catch (e) {
                      return 'Invalid Date';
                    }
                  })() : 'N/A'}</p>
                )}
                {driver.performance_score !== undefined && driver.performance_score !== null && (
                  <p><strong>{language === "ar" ? "نقاط الأداء:" : "Performance Score:"}</strong> {driver.performance_score.toFixed(1)}/10</p>
                )}
              </div>
            </div>

          </div>

          <DialogFooter className="mt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              {language === "ar" ? "إلغاء" : "Cancel"}
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (language === "ar" ? "جاري التحديث..." : "Updating...") : language === "ar" ? "تحديث" : "Update"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}