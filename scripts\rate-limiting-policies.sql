-- Rate Limiting Policies for Supabase Tables
-- This script implements basic rate limiting using Supabase policies and triggers
-- Note: For advanced rate limiting, consider using pg_bouncer or external services like Cloudflare

-- Enable necessary extensions (run once)
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create rate limit tracking table
CREATE TABLE IF NOT EXISTS rate_limit_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  table_name TEXT NOT NULL,
  action TEXT NOT NULL, -- INSERT, UPDATE, DELETE, SELECT
  request_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  ip_address INET,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Function to check rate limit (max 100 requests per minute per user per table)
CREATE OR REPLACE FUNCTION check_rate_limit(user_id_param UUID, table_name_param TEXT, action_param TEXT)
RETURNS BOOLEAN AS $$
DECLARE
  minute_start TIMESTAMP WITH TIME ZONE;
  request_count INTEGER;
BEGIN
  minute_start := date_trunc('minute', NOW());
  
  SELECT COUNT(*) INTO request_count
  FROM rate_limit_logs
  WHERE user_id = user_id_param
    AND table_name = table_name_param
    AND action = action_param
    AND request_time >= minute_start;
  
  -- Log the current request
  INSERT INTO rate_limit_logs (user_id, table_name, action, ip_address)
  VALUES (user_id_param, table_name_param, action_param, inet_client_addr());
  
  RETURN request_count < 100; -- Allow up to 100 requests per minute
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Example policy for vehicles table (INSERT with rate limiting)
-- Note: Supabase policies can't directly call functions with complex logic,
-- so this uses a trigger approach for demonstration

-- Create trigger function for vehicles INSERT rate limiting
CREATE OR REPLACE FUNCTION rate_limit_vehicles_insert()
RETURNS TRIGGER AS $$
BEGIN
  IF NOT check_rate_limit(auth.uid(), 'vehicles', 'INSERT') THEN
    RAISE EXCEPTION 'Rate limit exceeded for vehicles INSERT. Please wait and try again.';
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger on vehicles table
CREATE TRIGGER vehicles_rate_limit_insert
  BEFORE INSERT ON vehicles
  FOR EACH ROW
  EXECUTE FUNCTION rate_limit_vehicles_insert();

-- Similar for UPDATE
CREATE OR REPLACE FUNCTION rate_limit_vehicles_update()
RETURNS TRIGGER AS $$
BEGIN
  IF NOT check_rate_limit(auth.uid(), 'vehicles', 'UPDATE') THEN
    RAISE EXCEPTION 'Rate limit exceeded for vehicles UPDATE. Please wait and try again.';
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER vehicles_rate_limit_update
  BEFORE UPDATE ON vehicles
  FOR EACH ROW
  EXECUTE FUNCTION rate_limit_vehicles_update();

-- For users table (more restrictive: 50 requests per minute)
CREATE OR REPLACE FUNCTION check_user_rate_limit(user_id_param UUID, table_name_param TEXT, action_param TEXT)
RETURNS BOOLEAN AS $$
DECLARE
  minute_start TIMESTAMP WITH TIME ZONE;
  request_count INTEGER;
BEGIN
  minute_start := date_trunc('minute', NOW());
  
  SELECT COUNT(*) INTO request_count
  FROM rate_limit_logs
  WHERE user_id = user_id_param
    AND table_name = table_name_param
    AND action = action_param
    AND request_time >= minute_start;
  
  INSERT INTO rate_limit_logs (user_id, table_name, action, ip_address)
  VALUES (user_id_param, table_name_param, action_param, inet_client_addr());
  
  RETURN request_count < 50; -- Stricter limit for users table
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for users INSERT
CREATE OR REPLACE FUNCTION rate_limit_users_insert()
RETURNS TRIGGER AS $$
BEGIN
  IF NOT check_user_rate_limit(auth.uid(), 'users', 'INSERT') THEN
    RAISE EXCEPTION 'Rate limit exceeded for users INSERT. Please wait and try again.';
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER users_rate_limit_insert
  BEFORE INSERT ON users
  FOR EACH ROW
  EXECUTE FUNCTION rate_limit_users_insert();

-- Policy to enable RLS on rate_limit_logs (only admins can view logs)
ALTER TABLE rate_limit_logs ENABLE ROW LEVEL SECURITY;

-- Fixed policy to avoid infinite recursion
-- Use a simpler approach that doesn't query the users table within the policy
CREATE POLICY "Admin can view rate limit logs" ON rate_limit_logs
  FOR ALL TO authenticated
  USING (
    -- Allow users to see their own rate limit logs
    auth.uid() = user_id
    -- Note: For admin access, consider using service role key or a separate admin function
    -- OR auth.jwt() ->> 'role' = 'admin' -- if role is stored in JWT claims
  );

-- Cleanup old logs (keep only last 24 hours)
CREATE OR REPLACE FUNCTION cleanup_rate_limit_logs()
RETURNS void AS $$
BEGIN
  DELETE FROM rate_limit_logs WHERE created_at < NOW() - INTERVAL '24 hours';
END;
$$ LANGUAGE plpgsql;

-- Schedule cleanup (run via cron or Supabase edge function)
-- SELECT cron.schedule('cleanup-rate-logs', '0 * * * *', 'SELECT cleanup_rate_limit_logs();');

-- Usage Notes:
-- 1. Run this script in Supabase SQL Editor
-- 2. Ensure RLS is enabled on all tables
-- 3. For production, consider using pg_bouncer for connection pooling
-- 4. Monitor rate_limit_logs table for abuse patterns
-- 5. Adjust limits based on your application's needs
-- 6. For SELECT operations, rate limiting is harder to implement at DB level
--    Consider implementing at application level for read operations