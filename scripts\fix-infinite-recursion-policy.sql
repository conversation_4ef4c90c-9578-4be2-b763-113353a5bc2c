-- Fix infinite recursion in rate_limit_logs RLS policy
-- This script fixes the problematic policy that causes infinite recursion
-- by removing the subquery to the users table

-- Drop the problematic policy if it exists
DROP POLICY IF EXISTS "Admin can view rate limit logs" ON rate_limit_logs;

-- Create a new policy that doesn't cause infinite recursion
CREATE POLICY "Users can view their own rate limit logs" ON rate_limit_logs
  FOR ALL TO authenticated
  USING (
    -- Allow users to see their own rate limit logs
    auth.uid() = user_id
  );

-- Optional: Create a separate policy for admin access if needed
-- This would require storing the admin role in JWT claims or using service role
-- CREATE POLICY "Admin can view all rate limit logs" ON rate_limit_logs
--   FOR ALL TO authenticated
--   USING (
--     auth.jwt() ->> 'role' = 'admin'
--   );

-- Note: For full admin access to rate limit logs, consider:
-- 1. Using the service role key in admin functions
-- 2. Storing user roles in JWT claims during authentication
-- 3. Creating a separate admin-only view or function
