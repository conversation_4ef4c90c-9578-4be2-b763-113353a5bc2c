"use client"

import { useState, Suspense, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Eye, EyeOff, Loader2, Languages, Car, ArrowLeft } from "lucide-react"
import { useLanguage } from "@/contexts/language-context"
import { useAuth } from "@/contexts/auth-context"
import { ModeToggle } from "@/components/mode-toggle"
import { cnWithFont, getIconMargin } from "@/lib/utils"
import Link from "next/link"

function SignUpForm() {
  const router = useRouter()
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const { t, language, setLanguage, isRTL } = useLanguage()
  const { signUp, user } = useAuth()
  
  // Redirect to dashboard if already logged in
  useEffect(() => {
    if (user) {
      router.push('/')
    }
  }, [user, router])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)
    setSuccess(null)

    // Validate passwords match
    if (password !== confirmPassword) {
      setError(t("signup.passwordMismatch"))
      return
    }

    // Validate password strength
    if (password.length < 6) {
      setError(t("signup.passwordTooShort"))
      return
    }

    setIsLoading(true)

    try {
      await signUp(email, password)
      setSuccess(t("signup.successMessage"))
      // Clear form
      setEmail("")
      setPassword("")
      setConfirmPassword("")
    } catch (error: any) {
      console.error('Sign up error:', error)
      // Provide more specific error messages based on the error type
      if (error.message.includes('Database error')) {
        setError(t("signup.databaseErrorMessage") + " " + t("signup.tryAgainMessage"))
      } else if (error.message.includes('already registered')) {
        setError(t("signup.userExistsMessage"))
      } else {
        setError(error.message || t("signup.errorMessage"))
      }
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <div className={`absolute top-4 flex items-center gap-2 ${isRTL ? 'left-4' : 'right-4'}`}>
        <Button variant="outline" size="sm" onClick={() => setLanguage(language === "ar" ? "en" : "ar")}>
          <Languages className={`h-4 w-4 ${getIconMargin(isRTL)}`} />
          <span className={cnWithFont(language)}>
            {language === "ar" ? "English" : "العربية"}
          </span>
        </Button>
        <ModeToggle />
      </div>
      <div className="w-full max-w-md px-4">
        <div className="text-center mb-4">
          <div className="flex items-center justify-center mb-2">
            <div className="flex aspect-square size-12 items-center justify-center rounded-lg bg-primary text-primary-foreground">
              <Car className="size-6" />
            </div>
          </div>
          <h1 className={cnWithFont(language, "text-2xl font-bold text-foreground mb-1")}>
            {t("signup.title")}
          </h1>
        </div>

        <Card className="shadow-lg">
          <CardHeader className="space-y-1">
            <div className={cnWithFont(language, "text-center mb-1")}>
              <p className={cnWithFont(language, "text-lg font-medium text-muted-foreground")}>
                {t("signup.createAccount")}
              </p>
            </div>
          </CardHeader>
          <form onSubmit={handleSubmit}>
            <CardContent className="space-y-4">
              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {success && (
                <Alert variant="default" className="bg-green-100 border-green-500 text-green-800">
                  <AlertDescription>{success}</AlertDescription>
                </Alert>
              )}

              <div className="space-y-1">
                <Label htmlFor="email" className={cnWithFont(language)}>{t("signup.email")}</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder={t("signup.emailPlaceholder")}
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  className="h-11"
                  dir={t("common.dir") as "ltr" | "rtl"}
                />
              </div>

              <div className="space-y-1">
                <Label htmlFor="password" className={cnWithFont(language)}>{t("signup.password")}</Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    placeholder={t("signup.passwordPlaceholder")}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    required
                    className="h-11 pr-10"
                    dir={t("common.dir") as "ltr" | "rtl"}
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 end-0 flex items-center pr-3"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4 text-muted-foreground" />
                    ) : (
                      <Eye className="h-4 w-4 text-muted-foreground" />
                    )}
                  </button>
                </div>
              </div>

              <div className="space-y-1">
                <Label htmlFor="confirmPassword" className={cnWithFont(language)}>{t("signup.confirmPassword")}</Label>
                <div className="relative">
                  <Input
                    id="confirmPassword"
                    type={showConfirmPassword ? "text" : "password"}
                    placeholder={t("signup.confirmPasswordPlaceholder")}
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    required
                    className="h-11 pr-10"
                    dir={t("common.dir") as "ltr" | "rtl"}
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 end-0 flex items-center pr-3"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? (
                      <EyeOff className="h-4 w-4 text-muted-foreground" />
                    ) : (
                      <Eye className="h-4 w-4 text-muted-foreground" />
                    )}
                  </button>
                </div>
              </div>

              <Button
                type="submit"
                className={cnWithFont(language, "w-full h-11 mt-2")}
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {t("signup.creatingAccount")}
                  </>
                ) : (
                  t("signup.createAccountButton")
                )}
              </Button>

              <div className={`text-center pt-2 ${cnWithFont(language)}`}>
                <Link 
                  href="/login" 
                  className="text-sm text-primary hover:underline flex items-center justify-center"
                >
                  <ArrowLeft className={`h-4 w-4 me-1 ${isRTL ? 'rotate-180' : ''}`} />
                  {t("signup.backToLogin")}
                </Link>
              </div>
            </CardContent>
          </form>
        </Card>
      </div>
    </div>
  )
}

export default function SignUpPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    }>
      <SignUpForm />
    </Suspense>
  )
}