"use client"

import React, { useState, useEffect } from "react"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Skeleton } from "@/components/ui/skeleton"
import { useToast } from "@/hooks/use-toast"
import {
  X,
  Calendar,
  Image as ImageIcon,
  Car,
  Fuel,
  Palette,
  Gauge,
  Shield,
  Info,
  Clock,
  AlertTriangle,
  FileText,
  Settings,
  Activity,
  Download,
  Eye,
  Loader2
} from "lucide-react"
import { getVehicleLicenseImages } from "@/lib/vehicle-license-service"
import { Vehicle } from "@/lib/supabase"
import jsPDF from 'jspdf'
import { createEnhancedPDFHandler, setupArabicPDFSupport, applyCairoFontForArabic } from "@/lib/pdf-arabic-utils"

// Add Arabic font support to jsPDF
declare module 'jspdf' {
  interface jsPDF {
    addArabicText: (text: string, x: number, y: number, options?: any) => void
  }
}

interface VehicleProfileDialogProps {
  vehicle: Vehicle
  language: "ar" | "en"
  open: boolean
  onClose: () => void
}

interface LicenseImages {
  front?: string
  back?: string
}

export function VehicleProfileDialog({ vehicle, language, open, onClose }: VehicleProfileDialogProps) {
  const [licenseImages, setLicenseImages] = useState<LicenseImages>({})
  const [loadingImages, setLoadingImages] = useState(false)
  const [imageError, setImageError] = useState<string | null>(null)
  const [selectedImage, setSelectedImage] = useState<string | null>(null)
  const [generatingPDF, setGeneratingPDF] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    if (open && vehicle?.id) {
      loadLicenseImages()
    }
  }, [open, vehicle?.id])

  const loadLicenseImages = async () => {
    if (!vehicle?.id) return

    setLoadingImages(true)
    setImageError(null)
    try {
      const imageData = await getVehicleLicenseImages(vehicle.id)

      if (!imageData.error) {
        setLicenseImages({
          front: imageData.frontImage,
          back: imageData.backImage
        })
      } else {
        setImageError(imageData.error)
      }
    } catch (error) {
      console.error('Error loading license images:', error)
      setImageError(language === "ar" ? "خطأ في تحميل الصور" : "Error loading images")
    } finally {
      setLoadingImages(false)
    }
  }

  const formatDate = (dateString?: string) => {
    if (!dateString) return language === "ar" ? "غير محدد" : "Not specified"
    try {
      return new Date(dateString).toLocaleDateString(language === "ar" ? "ar-SA" : "en-US")
    } catch {
      return language === "ar" ? "تاريخ غير صالح" : "Invalid date"
    }
  }

  const formatNumber = (num?: number) => {
    if (num === undefined || num === null) return language === "ar" ? "غير متاح" : "N/A"
    if (num === 0) return "0"
    return num.toLocaleString()
  }

  const formatText = (text?: string) => {
    if (!text || text.trim() === "") return language === "ar" ? "غير محدد" : "Not specified"
    return text.trim()
  }

  const getMaintenanceProgress = () => {
    if (!vehicle.current_km || !vehicle.next_maintenance_km) return 0
    return Math.min((vehicle.current_km / vehicle.next_maintenance_km) * 100, 100)
  }

  const getMaintenanceStatus = () => {
    const progress = getMaintenanceProgress()
    if (progress >= 95) return { status: "critical", color: "destructive" }
    if (progress >= 85) return { status: "warning", color: "warning" }
    if (progress >= 70) return { status: "attention", color: "secondary" }
    return { status: "good", color: "default" }
  }

  const getDaysUntilExpiry = (dateString?: string) => {
    if (!dateString) return null
    try {
      const expiryDate = new Date(dateString)
      const today = new Date()
      const diffTime = expiryDate.getTime() - today.getTime()
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
      return diffDays
    } catch {
      return null
    }
  }

  const getExpiryStatus = (dateString?: string) => {
    const days = getDaysUntilExpiry(dateString)
    if (days === null) return { status: "unknown", variant: "secondary" as const }
    if (days < 0) return { status: "expired", variant: "destructive" as const }
    if (days <= 7) return { status: "critical", variant: "destructive" as const }
    if (days <= 30) return { status: "warning", variant: "secondary" as const }
    return { status: "valid", variant: "default" as const }
  }

  const handleImageView = (imageUrl: string) => {
    setSelectedImage(imageUrl)
  }

  const handleDownloadImage = async (imageUrl: string, imageName: string) => {
    try {
      // Fetch the image
      const response = await fetch(imageUrl)
      const blob = await response.blob()
      
      // Create a temporary URL for the blob
      const url = window.URL.createObjectURL(blob)
      
      // Create a temporary anchor element
      const a = document.createElement('a')
      a.href = url
      a.download = imageName
      
      // Trigger the download
      document.body.appendChild(a)
      a.click()
      
      // Clean up
      document.body.removeChild(a)
      window.URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Error downloading image:', error)
      toast({
        title: language === "ar" ? "خطأ في التنزيل" : "Download Error",
        description: language === "ar" ? "حدث خطأ أثناء تنزيل الصورة" : "An error occurred while downloading the image",
        variant: "destructive"
      })
    }
  }

  const generatePDF = async () => {
    setGeneratingPDF(true)
    try {
      const pdf = new jsPDF('p', 'mm', 'a4')
      // Force English language for PDF generation
      const isArabic = false // language === "ar"
      const pageWidth = pdf.internal.pageSize.getWidth()
      const pageHeight = pdf.internal.pageSize.getHeight()

      // Initialize Arabic PDF support
      await setupArabicPDFSupport(pdf, isArabic)
      applyCairoFontForArabic(pdf, isArabic)

      // Create enhanced PDF handler with Arabic support
      const pdfHandler = createEnhancedPDFHandler(pdf, isArabic)
      const addArabicText = (text: string, x: number, y: number, options: any = {}) => {
        pdfHandler.addText(text, x, y, options)
      }

      // Colors for different sections (as tuples for proper TypeScript support)
      const colors = {
        primary: [41, 98, 255] as [number, number, number], // Blue
        maintenance: [245, 158, 11] as [number, number, number], // Amber
        license: [34, 197, 94] as [number, number, number], // Green
        warning: [239, 68, 68] as [number, number, number], // Red
        text: [55, 65, 81] as [number, number, number], // Gray-700
        lightBg: [249, 250, 251] as [number, number, number], // Gray-50
        border: [229, 231, 235] as [number, number, number] // Gray-200
      }

      // Professional Header with Company Branding
      pdf.setFillColor(...colors.primary)
      pdf.rect(0, 0, pageWidth, 35, 'F')

      // Header content with improved Arabic support and reduced spacing
      pdf.setTextColor(255, 255, 255)
      // Force English text
      const headerTitle = "Comprehensive Vehicle Profile"
      addArabicText(headerTitle, 20, 20, { // Reduced y from 22 to 20
        font: 'helvetica',
        style: 'bold',
        fontSize: 20,
        align: 'left'
      })

      // Vehicle plate number in header
      const plateText = `Plate: ${formatText(vehicle.plate_number)}`
      addArabicText(plateText, 20, 27, { // Reduced y from 30 to 27
        font: 'helvetica',
        style: 'normal',
        fontSize: 12,
        align: 'left'
      })

      // Reset text color
      pdf.setTextColor(...colors.text)

      let yPosition = 35 // Reduced from 50 to 35

      // Executive Summary Section with reduced spacing
      pdf.setFillColor(...colors.lightBg)
      pdf.rect(15, yPosition - 3.5, pageWidth - 30, 17.5, 'F') // Reduced height by 30%
      pdf.setDrawColor(...colors.border)
      pdf.rect(15, yPosition - 3.5, pageWidth - 30, 17.5, 'S')

      // Executive Summary Section with Arabic support
      const summaryTitle = "Executive Summary"
      yPosition = pdfHandler.addSectionHeader(summaryTitle, 15, yPosition - 3.5, pageWidth - 30, colors.primary)

      pdf.setTextColor(...colors.text)
      pdf.setFontSize(9) // Reduced from 10 to 9

      // Key metrics in summary
      const currentKm = formatNumber(vehicle.current_km)
      const maintenanceProgress = getMaintenanceProgress().toFixed(0)
      const licenseStatus = (() => {
        const days = getDaysUntilExpiry(vehicle.license_expiry)
        if (days === null) return "N/A"
        if (days < 0) return "Expired"
        return `${days} days remaining`
      })()

      const summaryText = `Vehicle: ${formatText(vehicle.vehicle_type)} | Mileage: ${currentKm} km | Maintenance: ${maintenanceProgress}% | License: ${licenseStatus}`

      addArabicText(summaryText, 20, yPosition + 8.4, { // Reduced y offset
        font: 'helvetica',
        style: 'normal',
        fontSize: 9,
        align: 'left'
      })

      yPosition += 24.5 // Reduced from 35 to 24.5

      // Basic Information Section with Blue Theme and Arabic support (reduced spacing)
      const basicInfoTitle = "Basic Information"
      yPosition = pdfHandler.addSectionHeader(basicInfoTitle, 15, yPosition, pageWidth - 30, colors.primary)

      yPosition += 4.9 // Reduced from 7 to 4.9
      pdf.setTextColor(...colors.text)

      const basicInfo = [
        { label: "Plate Number", value: formatText(vehicle.plate_number) },
        { label: "Vehicle Type", value: formatText(vehicle.vehicle_type) },
        { label: "Fuel Type", value: formatText(vehicle.fuel_type) },
        { label: "Color", value: formatText(vehicle.color) },
        { label: "Status", value: vehicle.status },
      ]

      basicInfo.forEach((info, index) => {
        yPosition = pdfHandler.addDataRow(info.label, info.value, 15, yPosition, pageWidth - 30, index % 2 === 0)
      })

      yPosition += 7 // Reduced from 10 to 7

      // Mileage Information Section with Amber Theme and Arabic support (reduced spacing)
      const mileageTitle = "Mileage & Maintenance Information"
      yPosition = pdfHandler.addSectionHeader(mileageTitle, 15, yPosition, pageWidth - 30, colors.maintenance)

      yPosition += 4.9 // Reduced from 7 to 4.9
      pdf.setTextColor(...colors.text)

      const mileageInfo = [
        { label: "Current Mileage", value: `${formatNumber(vehicle.current_km)} km` },
        { label: "Last Maintenance", value: `${formatNumber(vehicle.last_maintenance_km)} km` },
        { label: "Next Maintenance", value: `${formatNumber(vehicle.next_maintenance_km)} km` },
        { label: "Maintenance Progress", value: `${getMaintenanceProgress().toFixed(1)}%` },
      ]

      mileageInfo.forEach((info, index) => {
        yPosition = pdfHandler.addDataRow(info.label, info.value, 15, yPosition, pageWidth - 30, index % 2 === 0)
      })

      // Maintenance Status Alert with Arabic support
      const progress = getMaintenanceProgress()
      if (progress >= 85) {
        yPosition += 3.5 // Reduced from 5 to 3.5
        const alertColor = progress >= 95 ? colors.warning : colors.maintenance
        const alertText = progress >= 95
          ? "⚠️ Urgent Maintenance Required!"
          : "⚠️ Maintenance Due Soon"
        yPosition = pdfHandler.addAlertBox(alertText, 15, yPosition, pageWidth - 30, alertColor)
      }

      yPosition += 7 // Reduced from 10 to 7

      // License Information Section with Green Theme and Arabic support
      const licenseTitle = "License & Registration Information"
      yPosition = pdfHandler.addSectionHeader(licenseTitle, 15, yPosition, pageWidth - 30, colors.license)

      yPosition += 7
      pdf.setTextColor(...colors.text)

      const licenseInfo = [
        { label: "License Type", value: formatText(vehicle.license_type) },
        { label: "License Expiry Date", value: formatDate(vehicle.license_expiry) },
        { label: "License Status", value: licenseStatus },
      ]

      licenseInfo.forEach((info, index) => {
        yPosition = pdfHandler.addDataRow(info.label, info.value, 15, yPosition, pageWidth - 30, index % 2 === 0)
      })

      // License Status Alert with Arabic support
      const days = getDaysUntilExpiry(vehicle.license_expiry)
      if (days !== null && days <= 30) {
        yPosition += 5
        const alertColor = days < 0 ? colors.warning : colors.maintenance
        const alertText = days < 0
          ? "⚠️ License Expired!"
          : "⚠️ License Expiring Soon!"
        yPosition = pdfHandler.addAlertBox(alertText, 15, yPosition, pageWidth - 30, alertColor)
      }

      // Additional Details Section with Arabic support (if available)
      if (vehicle.year || vehicle.vin || vehicle.service_type || vehicle.vehicle_features) {
        yPosition += 10

        const additionalTitle = "Additional Details"
        yPosition = pdfHandler.addSectionHeader(additionalTitle, 15, yPosition, pageWidth - 30, colors.text)

        yPosition += 7
        pdf.setTextColor(...colors.text)

        const additionalInfo = [
          ...(vehicle.year ? [{ label: "Manufacturing Year", value: vehicle.year.toString() }] : []),
          ...(vehicle.vin ? [{ label: "VIN Number", value: vehicle.vin }] : []),
          ...(vehicle.service_type ? [{ label: "Service Type", value: vehicle.service_type }] : []),
        ]

        additionalInfo.forEach((info, index) => {
          yPosition = pdfHandler.addDataRow(info.label, info.value, 15, yPosition, pageWidth - 30, index % 2 === 0)
        })

        if (vehicle.vehicle_features) {
          yPosition += 5
          const featuresTitle = "Features & Specifications:"
          addArabicText(featuresTitle, 20, yPosition, {
            font: 'helvetica',
            style: 'bold',
            fontSize: 9,
            align: 'left'
          })
          yPosition += 5.6 // Reduced from 8 to 5.6

          // Ensure vehicle_features is a string before splitting
          const features = typeof vehicle.vehicle_features === 'string' 
            ? vehicle.vehicle_features.split('\n') 
            : [];
            
          for (const feature of features) {
            if (feature && feature.trim()) {
              addArabicText(`• ${feature.trim()}`, 25, yPosition, {
                font: 'helvetica',
                style: 'normal',
                fontSize: 8,
                align: 'left'
              })
              yPosition += 4.2 // Reduced from 6 to 4.2
            }
          }
        }
      }

      // Professional Footer with Arabic support
      const footerY = pageHeight - 20
      pdf.setDrawColor(...colors.border)
      pdf.line(15, footerY - 5, pageWidth - 15, footerY - 5)

      pdf.setFontSize(8)
      pdf.setTextColor(...colors.text)

      const currentDate = new Date()
      const dateText = `Report generated on: ${currentDate.toLocaleDateString('en-US')} at ${currentDate.toLocaleTimeString('en-US')}`

      addArabicText(dateText, 20, footerY, {
        font: 'helvetica',
        style: 'normal',
        fontSize: 8,
        align: 'left'
      })

      const pageText = "Page 1 of 1"
      addArabicText(pageText, pageWidth - 20, footerY, {
        font: 'helvetica',
        style: 'normal',
        fontSize: 8,
        align: 'right'
      })

      // Save PDF with proper filename
      const fileName = `vehicle-profile-${vehicle.plate_number || 'unknown'}-${currentDate.toISOString().split('T')[0]}.pdf`
      pdf.save(fileName)

      // Force English toast messages
      toast({
        title: "Report Generated Successfully",
        description: "Professional PDF report downloaded successfully",
      })
    } catch (error) {
      console.error('Error generating PDF:', error)
      // Force English error messages
      toast({
        title: "Error Generating Report",
        description: "An error occurred while generating the professional PDF",
        variant: "destructive"
      })
    } finally {
      setGeneratingPDF(false)
    }
  }

  return (
    <>
      <Dialog open={open} onOpenChange={onClose}>
        <DialogContent className={`max-w-5xl max-h-[95vh] overflow-hidden ${language === "ar" ? "rtl" : "ltr"}`}>
          {/* Enhanced Header with Dark Mode Support */}
          <DialogHeader className="space-y-4 pb-6">
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-4">
                <div className="relative">
                  <div className="p-3 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-xl border border-blue-200 dark:border-blue-800">
                    <Car className="h-7 w-7 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div className="absolute -top-1 -right-1">
                    <Badge
                      variant={vehicle.status === "active" ? "default" : "secondary"}
                      className="h-5 px-1.5 text-xs"
                    >
                      {vehicle.status === "active" ? (language === "ar" ? "نشط" : "Active") : (language === "ar" ? "غير نشط" : "Inactive")}
                    </Badge>
                  </div>
                </div>
                <div className="space-y-1">
                  <DialogTitle className="text-2xl font-bold tracking-tight">
                    {language === "ar" ? "ملف المركبة" : "Vehicle Profile"}
                  </DialogTitle>
                  <DialogDescription className="text-base">
                    {vehicle.plate_number || (language === "ar" ? "رقم اللوحة غير متاح" : "Plate number not available")}
                  </DialogDescription>
                  <div className="flex items-center gap-2 mt-2">
                    <Badge variant="outline" className="text-xs">
                      {formatText(vehicle.vehicle_type)}
                    </Badge>
                    {vehicle.fuel_type && (
                      <Badge variant="outline" className="text-xs">
                        <Fuel className="h-3 w-3 mr-1" />
                        {formatText(vehicle.fuel_type)}
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={generatePDF}
                  disabled={generatingPDF}
                  className="h-8"
                >
                  {generatingPDF ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Download className="h-4 w-4" />
                  )}
                  <span className="ml-2 text-xs">
                    {language === "ar" ? "تحميل PDF" : "Download PDF"}
                  </span>
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onClose}
                  className="h-8 w-8 p-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
            <Separator className="dark:border-gray-700" />
          </DialogHeader>

        {/* Enhanced Main Content */}
        <ScrollArea className="flex-1 max-h-[calc(95vh-200px)]">
          <div className="space-y-6 pr-4">
            {/* Quick Stats Overview with Dark Mode Support */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card className="border-l-4 border-l-blue-500 dark:border-l-blue-400">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <Gauge className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground">{language === "ar" ? "الكيلومترات" : "Mileage"}</p>
                      <p className="text-lg font-bold">{formatNumber(vehicle.current_km)}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-l-4 border-l-green-500 dark:border-l-green-400">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-green-50 dark:bg-green-900/20 rounded-lg">
                      <Activity className="h-4 w-4 text-green-600 dark:text-green-400" />
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground">{language === "ar" ? "الحالة" : "Status"}</p>
                      <p className="text-sm font-semibold capitalize">{vehicle.status}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-l-4 border-l-amber-500 dark:border-l-amber-400">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-amber-50 dark:bg-amber-900/20 rounded-lg">
                      <Clock className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground">{language === "ar" ? "الصيانة" : "Maintenance"}</p>
                      <p className="text-sm font-semibold">{getMaintenanceProgress().toFixed(0)}%</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-l-4 border-l-purple-500 dark:border-l-purple-400">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                      <Shield className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground">{language === "ar" ? "الرخصة" : "License"}</p>
                      <p className="text-sm font-semibold">
                        {(() => {
                          const days = getDaysUntilExpiry(vehicle.license_expiry)
                          if (days === null) return language === "ar" ? "غير محدد" : "N/A"
                          if (days < 0) return language === "ar" ? "منتهية" : "Expired"
                          return `${days}d`
                        })()}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Vehicle Information Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Enhanced Basic Info Card with Dark Mode */}
              <Card className="hover:shadow-md dark:hover:shadow-lg transition-shadow">
                <CardHeader className="pb-4">
                  <CardTitle className="text-base font-semibold flex items-center gap-2">
                    <div className="p-1.5 bg-blue-50 dark:bg-blue-900/20 rounded-md">
                      <Info className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    </div>
                    {language === "ar" ? "المعلومات الأساسية" : "Basic Information"}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                      {language === "ar" ? "رقم اللوحة" : "Plate Number"}
                    </label>
                    <p className="font-semibold text-foreground">{formatText(vehicle.plate_number)}</p>
                  </div>
                  <Separator className="dark:border-gray-700" />
                  <div className="space-y-2">
                    <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                      {language === "ar" ? "نوع المركبة" : "Vehicle Type"}
                    </label>
                    <p className="font-medium">{formatText(vehicle.vehicle_type)}</p>
                  </div>
                  <div className="space-y-2">
                    <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                      {language === "ar" ? "نوع الوقود" : "Fuel Type"}
                    </label>
                    <div className="flex items-center gap-2">
                      <Fuel className="h-4 w-4 text-muted-foreground" />
                      <p className="font-medium">{formatText(vehicle.fuel_type)}</p>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                      {language === "ar" ? "اللون" : "Color"}
                    </label>
                    <div className="flex items-center gap-2">
                      <Palette className="h-4 w-4 text-muted-foreground" />
                      <p className="font-medium">{formatText(vehicle.color)}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Enhanced Mileage Card */}
              <Card className="hover:shadow-md transition-shadow">
                <CardHeader className="pb-4">
                  <CardTitle className="text-base font-semibold flex items-center gap-2">
                    <div className="p-1.5 bg-green-50 rounded-md">
                      <Gauge className="h-4 w-4 text-green-600" />
                    </div>
                    {language === "ar" ? "الكيلومترات" : "Mileage & Usage"}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                      {language === "ar" ? "الكيلومترات الحالية" : "Current Mileage"}
                    </label>
                    <div className="flex items-baseline gap-2">
                      <p className="text-2xl font-bold text-foreground">{formatNumber(vehicle.current_km)}</p>
                      <span className="text-sm text-muted-foreground">{language === "ar" ? "كم" : "km"}</span>
                    </div>
                  </div>
                  <Separator />
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-1">
                      <label className="text-xs font-medium text-muted-foreground">
                        {language === "ar" ? "آخر صيانة" : "Last Service"}
                      </label>
                      <p className="font-semibold text-sm">{formatNumber(vehicle.last_maintenance_km)} km</p>
                    </div>
                    <div className="space-y-1">
                      <label className="text-xs font-medium text-muted-foreground">
                        {language === "ar" ? "الصيانة القادمة" : "Next Service"}
                      </label>
                      <p className="font-semibold text-sm">{formatNumber(vehicle.next_maintenance_km)} km</p>
                    </div>
                  </div>
                  {vehicle.last_tire_change_km && (
                    <>
                      <Separator />
                      <div className="space-y-1">
                        <label className="text-xs font-medium text-muted-foreground">
                          {language === "ar" ? "آخر تغيير إطارات" : "Last Tire Change"}
                        </label>
                        <p className="font-medium text-sm">{formatNumber(vehicle.last_tire_change_km)} km</p>
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>

              {/* Enhanced Status & License Card */}
              <Card className="hover:shadow-md transition-shadow">
                <CardHeader className="pb-4">
                  <CardTitle className="text-base font-semibold flex items-center gap-2">
                    <div className="p-1.5 bg-purple-50 rounded-md">
                      <Shield className="h-4 w-4 text-purple-600" />
                    </div>
                    {language === "ar" ? "الحالة والرخصة" : "Status & License"}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                      {language === "ar" ? "حالة المركبة" : "Vehicle Status"}
                    </label>
                    <div className="flex items-center gap-2">
                      <div className={`w-2 h-2 rounded-full ${vehicle.status === "active" ? "bg-green-500" : "bg-gray-400"}`} />
                      <Badge variant={vehicle.status === "active" ? "default" : "secondary"} className="font-medium">
                        {vehicle.status === "active" ? (language === "ar" ? "نشط" : "Active") : (language === "ar" ? "غير نشط" : "Inactive")}
                      </Badge>
                    </div>
                  </div>
                  <Separator />
                  <div className="space-y-2">
                    <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                      {language === "ar" ? "نوع الرخصة" : "License Type"}
                    </label>
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4 text-muted-foreground" />
                      <p className="font-medium">{formatText(vehicle.license_type)}</p>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                      {language === "ar" ? "انتهاء الرخصة" : "License Expiry"}
                    </label>
                    <div className="flex items-center justify-between">
                      <p className="font-medium">{formatDate(vehicle.license_expiry)}</p>
                      {(() => {
                        const expiryStatus = getExpiryStatus(vehicle.license_expiry)
                        const days = getDaysUntilExpiry(vehicle.license_expiry)
                        if (days !== null) {
                          return (
                            <Badge variant={expiryStatus.variant} className="text-xs">
                              {days > 0
                                ? `${days} ${language === "ar" ? "يوم" : "days"}`
                                : language === "ar" ? "منتهية" : "Expired"
                              }
                            </Badge>
                          )
                        }
                        return null
                      })()}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Enhanced Maintenance Progress Section */}
            <Card className="border-l-4 border-l-amber-500">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="p-1.5 bg-amber-50 rounded-md">
                      <Settings className="h-5 w-5 text-amber-600" />
                    </div>
                    {language === "ar" ? "حالة الصيانة" : "Maintenance Status"}
                  </div>
                  <Badge variant={getMaintenanceStatus().color as any} className="font-medium">
                    {getMaintenanceProgress().toFixed(0)}%
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">
                      {language === "ar" ? "التقدم نحو الصيانة القادمة" : "Progress to Next Service"}
                    </span>
                    <span className="text-sm text-muted-foreground font-mono">
                      {getMaintenanceProgress().toFixed(1)}%
                    </span>
                  </div>
                  <Progress
                    value={getMaintenanceProgress()}
                    className="h-3 bg-gray-100"
                  />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span className="font-medium">{formatNumber(vehicle.current_km)} km</span>
                    <span className="font-medium">{formatNumber(vehicle.next_maintenance_km)} km</span>
                  </div>
                </div>

                {/* Maintenance Alerts with Dark Mode Support */}
                {(() => {
                  const progress = getMaintenanceProgress()

                  if (progress >= 95) {
                    return (
                      <div className="flex items-start gap-3 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                        <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0" />
                        <div>
                          <p className="text-sm font-medium text-red-800 dark:text-red-200">
                            {language === "ar" ? "صيانة عاجلة مطلوبة" : "Urgent Maintenance Required"}
                          </p>
                          <p className="text-xs text-red-700 dark:text-red-300 mt-1">
                            {language === "ar" ? "تجاوزت المركبة الحد المسموح للصيانة" : "Vehicle has exceeded maintenance threshold"}
                          </p>
                        </div>
                      </div>
                    )
                  } else if (progress >= 85) {
                    return (
                      <div className="flex items-start gap-3 p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg">
                        <Clock className="h-5 w-5 text-amber-600 dark:text-amber-400 mt-0.5 flex-shrink-0" />
                        <div>
                          <p className="text-sm font-medium text-amber-800 dark:text-amber-200">
                            {language === "ar" ? "صيانة مطلوبة قريباً" : "Maintenance Due Soon"}
                          </p>
                          <p className="text-xs text-amber-700 dark:text-amber-300 mt-1">
                            {language === "ar" ? "يُنصح بجدولة الصيانة في أقرب وقت" : "Schedule maintenance at your earliest convenience"}
                          </p>
                        </div>
                      </div>
                    )
                  } else if (progress >= 70) {
                    return (
                      <div className="flex items-start gap-3 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                        <Info className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
                        <div>
                          <p className="text-sm font-medium text-blue-800 dark:text-blue-200">
                            {language === "ar" ? "اقتراب موعد الصيانة" : "Maintenance Approaching"}
                          </p>
                          <p className="text-xs text-blue-700 dark:text-blue-300 mt-1">
                            {language === "ar" ? "ابدأ في التخطيط للصيانة القادمة" : "Start planning for upcoming maintenance"}
                          </p>
                        </div>
                      </div>
                    )
                  }
                  return null
                })()}
              </CardContent>
            </Card>

            {/* Enhanced Important Dates & Information Section with Dark Mode */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="hover:shadow-md dark:hover:shadow-lg transition-shadow">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-2">
                    <div className="p-1.5 bg-indigo-50 dark:bg-indigo-900/20 rounded-md">
                      <Calendar className="h-5 w-5 text-indigo-600 dark:text-indigo-400" />
                    </div>
                    {language === "ar" ? "التواريخ المهمة" : "Important Dates"}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="p-4 border dark:border-gray-700 rounded-lg bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900">
                    <div className="flex items-center justify-between">
                      <div className="space-y-1">
                        <p className="text-sm font-semibold flex items-center gap-2">
                          <Shield className="h-4 w-4 text-muted-foreground" />
                          {language === "ar" ? "انتهاء الرخصة" : "License Expiry"}
                        </p>
                        <p className="text-xs text-muted-foreground">{formatDate(vehicle.license_expiry)}</p>
                      </div>
                      {(() => {
                        const days = getDaysUntilExpiry(vehicle.license_expiry)
                        const status = getExpiryStatus(vehicle.license_expiry)
                        if (days === null) return null
                        return (
                          <Badge variant={status.variant} className="font-medium">
                            {days > 0
                              ? `${days} ${language === "ar" ? "يوم" : "days"}`
                              : language === "ar" ? "منتهية" : "Expired"
                            }
                          </Badge>
                        )
                      })()}
                    </div>
                  </div>

                  <div className="p-4 border dark:border-gray-700 rounded-lg bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
                    <div className="space-y-1">
                      <p className="text-sm font-semibold flex items-center gap-2">
                        <Settings className="h-4 w-4 text-muted-foreground" />
                        {language === "ar" ? "آخر صيانة" : "Last Maintenance"}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {formatNumber(vehicle.last_maintenance_km)} {language === "ar" ? "كم" : "km"}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Additional Vehicle Details with Dark Mode */}
              <Card className="hover:shadow-md dark:hover:shadow-lg transition-shadow">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-2">
                    <div className="p-1.5 bg-teal-50 dark:bg-teal-900/20 rounded-md">
                      <Car className="h-5 w-5 text-teal-600 dark:text-teal-400" />
                    </div>
                    {language === "ar" ? "تفاصيل إضافية" : "Additional Details"}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {vehicle.year && (
                    <div className="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-700">
                      <span className="text-sm text-muted-foreground">{language === "ar" ? "سنة الصنع" : "Year"}</span>
                      <span className="font-medium">{vehicle.year}</span>
                    </div>
                  )}
                  {vehicle.vin && (
                    <div className="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-700">
                      <span className="text-sm text-muted-foreground">{language === "ar" ? "رقم الهيكل" : "VIN"}</span>
                      <span className="font-mono text-sm">{vehicle.vin}</span>
                    </div>
                  )}
                  {vehicle.service_type && (
                    <div className="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-700">
                      <span className="text-sm text-muted-foreground">{language === "ar" ? "نوع الخدمة" : "Service Type"}</span>
                      <Badge variant="outline">{vehicle.service_type}</Badge>
                    </div>
                  )}
                  {vehicle.vehicle_features && (
                    <div className="space-y-2">
                      <span className="text-sm text-muted-foreground">{language === "ar" ? "المميزات" : "Features"}</span>
                      <p className="text-sm bg-gray-50 dark:bg-gray-800 p-2 rounded border dark:border-gray-700">{vehicle.vehicle_features}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Enhanced License Images Section with Dark Mode */}
            <Card className="hover:shadow-md dark:hover:shadow-lg transition-shadow">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="p-1.5 bg-emerald-50 dark:bg-emerald-900/20 rounded-md">
                      <ImageIcon className="h-5 w-5 text-emerald-600 dark:text-emerald-400" />
                    </div>
                    {language === "ar" ? "صور الرخصة" : "License Documents"}
                  </div>
                  {loadingImages && (
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                      {language === "ar" ? "جاري التحميل..." : "Loading..."}
                    </div>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {loadingImages ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-3">
                      <Skeleton className="h-4 w-20" />
                      <Skeleton className="aspect-video w-full rounded-lg" />
                    </div>
                    <div className="space-y-3">
                      <Skeleton className="h-4 w-20" />
                      <Skeleton className="aspect-video w-full rounded-lg" />
                    </div>
                  </div>
                ) : imageError ? (
                  <div className="flex items-center justify-center py-12 text-center">
                    <div className="space-y-3">
                      <AlertTriangle className="h-12 w-12 text-amber-500 dark:text-amber-400 mx-auto" />
                      <div>
                        <p className="font-medium text-foreground">{language === "ar" ? "خطأ في تحميل الصور" : "Error Loading Images"}</p>
                        <p className="text-sm text-muted-foreground mt-1">{imageError}</p>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-3">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                        <label className="text-sm font-semibold">{language === "ar" ? "الوجه الأمامي" : "Front Side"}</label>
                      </div>
                      <div className="group relative border-2 border-dashed border-muted-foreground/25 dark:border-muted-foreground/40 rounded-xl p-6 aspect-video flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 hover:from-gray-100 hover:to-gray-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all">
                        {licenseImages.front ? (
                          <div className="relative w-full h-full">
                            <Image
                              src={licenseImages.front!}
                              alt={language === "ar" ? "الوجه الأمامي للرخصة" : "License front side"}
                              fill
                              className="object-contain rounded-lg shadow-sm"
                              priority
                              sizes="100vw"
                            />
                            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 dark:group-hover:bg-black/20 transition-colors rounded-lg flex items-center justify-center">
                              <div className="opacity-0 group-hover:opacity-100 transition-opacity flex gap-2">
                                <Button
                                  variant="secondary"
                                  size="sm"
                                  className="shadow-lg"
                                  onClick={() => handleImageView(licenseImages.front!)}
                                >
                                  <Eye className="h-4 w-4 mr-2" />
                                  {language === "ar" ? "عرض" : "View"}
                                </Button>
                                <Button
                                  variant="default"
                                  size="sm"
                                  className="shadow-lg"
                                  onClick={() => handleDownloadImage(licenseImages.front!, `license-front-${vehicle.plate_number || 'unknown'}.jpg`)}
                                >
                                  <Download className="h-4 w-4 mr-2" />
                                  {language === "ar" ? "تحميل" : "Download"}
                                </Button>
                              </div>
                            </div>
                          </div>
                        ) : (
                          <div className="text-center text-muted-foreground">
                            <div className="p-4 bg-white dark:bg-gray-800 rounded-full mb-4 shadow-sm">
                              <ImageIcon className="h-8 w-8" />
                            </div>
                            <p className="font-medium">{language === "ar" ? "لا توجد صورة" : "No image available"}</p>
                            <p className="text-xs mt-1">{language === "ar" ? "لم يتم رفع صورة للوجه الأمامي" : "Front side image not uploaded"}</p>
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="space-y-3">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-green-500"></div>
                        <label className="text-sm font-semibold">{language === "ar" ? "الوجه الخلفي" : "Back Side"}</label>
                      </div>
                      <div className="group relative border-2 border-dashed border-muted-foreground/25 dark:border-muted-foreground/40 rounded-xl p-6 aspect-video flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 hover:from-gray-100 hover:to-gray-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all">
                        {licenseImages.back ? (
                          <div className="relative w-full h-full">
                            <Image
                              src={licenseImages.back!}
                              alt={language === "ar" ? "الوجه الخلفي للرخصة" : "License back side"}
                              fill
                              className="object-contain rounded-lg shadow-sm"
                              priority
                              sizes="100vw"
                            />
                            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 dark:group-hover:bg-black/20 transition-colors rounded-lg flex items-center justify-center">
                              <div className="opacity-0 group-hover:opacity-100 transition-opacity flex gap-2">
                                <Button
                                  variant="secondary"
                                  size="sm"
                                  className="shadow-lg"
                                  onClick={() => handleImageView(licenseImages.back!)}
                                >
                                  <Eye className="h-4 w-4 mr-2" />
                                  {language === "ar" ? "عرض" : "View"}
                                </Button>
                                <Button
                                  variant="default"
                                  size="sm"
                                  className="shadow-lg"
                                  onClick={() => handleDownloadImage(licenseImages.back!, `license-back-${vehicle.plate_number || 'unknown'}.jpg`)}
                                >
                                  <Download className="h-4 w-4 mr-2" />
                                  {language === "ar" ? "تحميل" : "Download"}
                                </Button>
                              </div>
                            </div>
                          </div>
                        ) : (
                          <div className="text-center text-muted-foreground">
                            <div className="p-4 bg-white dark:bg-gray-800 rounded-full mb-4 shadow-sm">
                              <ImageIcon className="h-8 w-8" />
                            </div>
                            <p className="font-medium">{language === "ar" ? "لا توجد صورة" : "No image available"}</p>
                            <p className="text-xs mt-1">{language === "ar" ? "لم يتم رفع صورة للوجه الخلفي" : "Back side image not uploaded"}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>

    {/* Image Viewer Modal */}
    <Dialog open={!!selectedImage} onOpenChange={() => setSelectedImage(null)}>
      <DialogContent className="max-w-4xl max-h-[90vh] p-0">
        <DialogHeader className="sr-only">
          <DialogTitle>
            {language === "ar" ? "عرض صورة الرخصة" : "License Image Viewer"}
          </DialogTitle>
        </DialogHeader>
        <div className="relative">
          {selectedImage && (
            <Image
              src={selectedImage!}
              alt={language === "ar" ? "صورة الرخصة" : "License image"}
              fill
              className="object-contain rounded-lg"
              priority
              sizes="100vw"
            />
          )}
        </div>
      </DialogContent>
    </Dialog>
  </>
  )
}