@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  /* RTL/LTR Direction Utilities */
  .rtl-flip {
    transform: scaleX(-1);
  }
  
  .ltr-flip {
    transform: scaleX(1);
  }
  
  /* Margin and Padding RTL/LTR utilities */
  [dir="rtl"] .ml-auto {
    margin-left: unset;
    margin-right: auto;
  }
  
  [dir="rtl"] .mr-auto {
    margin-right: unset;
    margin-left: auto;
  }
  
  [dir="rtl"] .pl-8 {
    padding-left: unset;
    padding-right: 2rem;
  }
  
  [dir="rtl"] .pr-3 {
    padding-right: unset;
    padding-left: 0.75rem;
  }
  
  [dir="rtl"] .pr-10 {
    padding-right: unset;
    padding-left: 2.5rem;
  }
  
  [dir="rtl"] .left-2 {
    left: unset;
    right: 0.5rem;
  }
  
  [dir="rtl"] .right-4 {
    right: unset;
    left: 1rem;
  }
  
  /* Text alignment for RTL */
  [dir="rtl"] .text-left {
    text-align: right;
  }
  
  [dir="rtl"] .text-right {
    text-align: left;
  }
  
  /* Flex direction for RTL */
  [dir="rtl"] .flex-row {
    flex-direction: row-reverse;
  }
  
  [dir="rtl"] .flex-row-reverse {
    flex-direction: row;
  }
  
  /* Sidebar specific RTL adjustments */
  [dir="rtl"] [data-sidebar="sidebar"] {
    border-right: 1px solid hsl(var(--sidebar-border));
    border-left: none;
  }
  
  [dir="ltr"] [data-sidebar="sidebar"] {
    border-left: 1px solid hsl(var(--sidebar-border));
    border-right: none;
  }
  
  /* SidebarTrigger positioning */
  [dir="rtl"] [data-sidebar="trigger"] {
    margin-right: -0.25rem;
    margin-left: unset;
  }
  
  [dir="ltr"] [data-sidebar="trigger"] {
    margin-left: -0.25rem;
    margin-right: unset;
  }
  
  /* Border radius adjustments for RTL */
  [dir="rtl"] .rounded-l {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
  }
  
  [dir="rtl"] .rounded-r {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    /* Default font for English - Segoe UI */
    font-family: "Segoe UI", -apple-system, BlinkMacSystemFont, system-ui, Roboto, "Helvetica Neue", Arial, sans-serif;
    transition: direction 0.3s ease;
  }
  
  /* Enhanced Arabic font styling with Cairo as primary font */
  .font-arabic, [lang="ar"] {
    font-family: var(--font-cairo), 'Cairo', "Segoe UI", -apple-system, BlinkMacSystemFont, system-ui, sans-serif;
  }
  
  /* English font styling */
  .font-english, [lang="en"] {
    font-family: "Segoe UI", -apple-system, BlinkMacSystemFont, system-ui, Roboto, "Helvetica Neue", Arial, sans-serif;
  }
  
  /* Language-specific body styling with direction */
  body[data-language="ar"], body.rtl {
    font-family: var(--font-cairo), 'Cairo', "Segoe UI", -apple-system, BlinkMacSystemFont, system-ui, sans-serif;
    direction: rtl;
  }
  
  body[data-language="en"], body.ltr {
    font-family: "Segoe UI", -apple-system, BlinkMacSystemFont, system-ui, Roboto, "Helvetica Neue", Arial, sans-serif;
    direction: ltr;
  }
  
  /* Ensure Cairo font is used for all Arabic text in PDF exports */
  .pdf-content [lang="ar"], .pdf-arabic, .arabic-content {
    font-family: 'Cairo', var(--font-cairo), "Segoe UI", -apple-system, BlinkMacSystemFont, system-ui, sans-serif !important;
  }
  
  /* RTL-specific adjustments */
  [dir="rtl"] {
    text-align: right;
  }
  
  [dir="ltr"] {
    text-align: left;
  }
  
  /* Sidebar RTL adjustments */
  [dir="rtl"] .sidebar {
    right: 0;
    left: unset;
  }
  
  [dir="ltr"] .sidebar {
    left: 0;
    right: unset;
  }
  
  /* Sidebar positioning for RTL */
  [dir="rtl"] [data-side="right"] {
    right: 0;
    left: unset;
  }
  
  [dir="ltr"] [data-side="left"] {
    left: 0;
    right: unset;
  }
  
  /* ChevronUp positioning for RTL */
  [dir="rtl"] .lucide-chevron-up {
    margin-right: auto;
    margin-left: unset;
  }
  
  [dir="ltr"] .lucide-chevron-up {
    margin-left: auto;
    margin-right: unset;
  }
  
  /* Form inputs RTL handling */
  [dir="rtl"] input[type="text"],
  [dir="rtl"] input[type="email"],
  [dir="rtl"] input[type="password"],
  [dir="rtl"] textarea {
    text-align: right;
  }
  
  [dir="ltr"] input[type="text"],
  [dir="ltr"] input[type="email"],
  [dir="ltr"] input[type="password"],
  [dir="ltr"] textarea {
    text-align: left;
  }
  
  /* Icon positioning for RTL */
  [dir="rtl"] .lucide {
    transform: scaleX(-1);
  }
  
  /* Exclude certain icons from flipping */
  [dir="rtl"] .lucide.no-flip {
    transform: none;
  }
}