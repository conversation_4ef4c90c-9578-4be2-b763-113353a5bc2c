{"name": "fleetlytic", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "build:production": "next build", "build:dev": "next build", "dev": "next dev", "lint": "next lint", "start": "next start", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@supabase/ssr": "^0.7.0", "@supabase/supabase-js": "^2.57.2", "@tanstack/react-query": "^5.87.1", "@types/bcryptjs": "^2.4.6", "bcryptjs": "^3.0.2", "@types/jspdf": "^1.3.3", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "latest", "dotenv": "^17.2.2", "embla-carousel-react": "8.5.1", "geist": "^1.3.1", "input-otp": "1.4.1", "jspdf": "^3.0.2", "lucide-react": "^0.542.0", "next": "15.2.4", "next-themes": "latest", "react": "^19", "react-day-picker": "9.8.0", "react-dom": "^19", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "xlsx": "^0.18.5", "zod": "^3.24.1"}, "devDependencies": {"@tanstack/react-query-devtools": "^5.59.0", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "^30.0.0", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.5", "tailwindcss": "^3.4.17", "typescript": "^5"}}