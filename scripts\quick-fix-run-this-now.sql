-- QUICK FIX: Run this complete script in Supabase SQL Editor
-- This will stop the infinite recursion errors immediately

-- Step 1: Disable <PERSON><PERSON> temporarily to stop recursion
ALTER TABLE users DISABLE ROW LEVEL SECURITY;

-- Step 2: Drop any existing policies that might be causing issues
DROP POLICY IF EXISTS "Users can access their own data" ON users;
DROP POLICY IF EXISTS "Branch managers can access branch users" ON users;
DROP POLICY IF EXISTS "Ad<PERSON> can access all users" ON users;
DROP POLICY IF EXISTS "Users can view users in same branch" ON users;
DROP POLICY IF EXISTS "Enable read access for all users" ON users;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON users;
DROP POLICY IF EXISTS "Enable update for users based on email" ON users;
DROP POLICY IF EXISTS "Enable delete for users based on email" ON users;

-- Step 3: Re-enable RLS
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Step 4: Create simple, safe policies
CREATE POLICY "Users can access own record" ON users
  FOR ALL TO authenticated
  USING (auth.uid() = id);

CREATE POLICY "Service role can access all users" ON users
  FOR ALL TO service_role
  USING (true);

-- Step 5: Fix other tables that might have problematic policies
-- Branches table
ALTER TABLE branches DISABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Branch access based on user branch" ON branches;
ALTER TABLE branches ENABLE ROW LEVEL SECURITY;
CREATE POLICY "All authenticated users can read branches" ON branches
  FOR SELECT TO authenticated
  USING (true);

-- Vehicles table
ALTER TABLE vehicles DISABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Vehicle access based on user branch" ON vehicles;
ALTER TABLE vehicles ENABLE ROW LEVEL SECURITY;
CREATE POLICY "All authenticated users can read vehicles" ON vehicles
  FOR SELECT TO authenticated
  USING (true);

-- Drivers table
ALTER TABLE drivers DISABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Driver access based on user branch" ON drivers;
ALTER TABLE drivers ENABLE ROW LEVEL SECURITY;
CREATE POLICY "All authenticated users can read drivers" ON drivers
  FOR SELECT TO authenticated
  USING (true);

-- Test query to verify it works
SELECT 'Fix applied successfully! You should now be able to query users without infinite recursion.' as status;
