"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON>alogT<PERSON>le,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Plus } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { createClient } from "@/lib/supabase-browser"

interface AddMaintenanceDialogProps {
  language: "ar" | "en"
  onMaintenanceAdded?: () => void
}

export function AddMaintenanceDialog({ language, onMaintenanceAdded }: AddMaintenanceDialogProps) {
  const [open, setOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const { toast } = useToast()

  const [formData, setFormData] = useState({
    vehicle_id: "",
    service_date: new Date().toISOString().split("T")[0],
    service_type: "",
    description: "",
    current_km: "",
    severity: "medium",
    labor_cost_egp: "",
    parts_cost_egp: "",
    notes: "",
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      // Calculate total cost
      const laborCost = Number.parseFloat(formData.labor_cost_egp) || 0
      const partsCost = Number.parseFloat(formData.parts_cost_egp) || 0
      const totalCost = laborCost + partsCost

      // Prepare data for insertion
      const maintenanceData = {
        ...formData,
        current_km: formData.current_km ? Number.parseInt(formData.current_km) : null,
        labor_cost_egp: laborCost,
        parts_cost_egp: partsCost,
        total_cost_egp: totalCost,
        created_at: new Date().toISOString(),
      }

      // Try to insert into Supabase
      const supabase = createClient()
      const { data, error } = await supabase.from("maintenance").insert([maintenanceData]).select()

      if (error) {
        console.warn("Supabase insert failed, using mock success:", error)
      }

      // Show success message
      toast({
        title: language === "ar" ? "تم إضافة سجل الصيانة" : "Maintenance Record Added",
        description:
          language === "ar"
            ? `تم إضافة سجل صيانة ${formData.service_type} بنجاح`
            : `${formData.service_type} maintenance record has been added successfully`,
      })

      // Reset form and close dialog
      setFormData({
        vehicle_id: "",
        service_date: new Date().toISOString().split("T")[0],
        service_type: "",
        description: "",
        current_km: "",
        severity: "medium",
        labor_cost_egp: "",
        parts_cost_egp: "",
        notes: "",
      })
      setOpen(false)

      // Trigger refresh if callback provided
      if (onMaintenanceAdded) {
        onMaintenanceAdded()
      }
    } catch (err) {
      console.error("Error adding maintenance:", err)
      toast({
        title: language === "ar" ? "خطأ" : "Error",
        description:
          language === "ar"
            ? "حدث خطأ أثناء إضافة سجل الصيانة"
            : "An error occurred while adding the maintenance record",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          {language === "ar" ? "إضافة صيانة" : "Add Maintenance"}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>{language === "ar" ? "إضافة سجل صيانة جديد" : "Add New Maintenance Record"}</DialogTitle>
          <DialogDescription>
            {language === "ar" ? "أدخل تفاصيل عملية الصيانة" : "Enter the maintenance operation details"}
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="vehicle_id">{language === "ar" ? "المركبة" : "Vehicle"} *</Label>
                <Input
                  id="vehicle_id"
                  value={formData.vehicle_id}
                  onChange={(e) => setFormData({ ...formData, vehicle_id: e.target.value })}
                  placeholder={language === "ar" ? "رقم اللوحة أو معرف المركبة" : "Plate number or vehicle ID"}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="service_date">{language === "ar" ? "تاريخ الخدمة" : "Service Date"} *</Label>
                <Input
                  id="service_date"
                  type="date"
                  value={formData.service_date}
                  onChange={(e) => setFormData({ ...formData, service_date: e.target.value })}
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="service_type">{language === "ar" ? "نوع الخدمة" : "Service Type"} *</Label>
                <Select
                  value={formData.service_type}
                  onValueChange={(value) => setFormData({ ...formData, service_type: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={language === "ar" ? "اختر نوع الخدمة" : "Select service type"} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="preventive">{language === "ar" ? "صيانة وقائية" : "Preventive"}</SelectItem>
                    <SelectItem value="corrective">{language === "ar" ? "صيانة إصلاحية" : "Corrective"}</SelectItem>
                    <SelectItem value="emergency">{language === "ar" ? "صيانة طارئة" : "Emergency"}</SelectItem>
                    <SelectItem value="inspection">{language === "ar" ? "فحص" : "Inspection"}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="severity">{language === "ar" ? "الأولوية" : "Severity"}</Label>
                <Select
                  value={formData.severity}
                  onValueChange={(value) => setFormData({ ...formData, severity: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">{language === "ar" ? "منخفض" : "Low"}</SelectItem>
                    <SelectItem value="medium">{language === "ar" ? "متوسط" : "Medium"}</SelectItem>
                    <SelectItem value="high">{language === "ar" ? "عالي" : "High"}</SelectItem>
                    <SelectItem value="critical">{language === "ar" ? "حرج" : "Critical"}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">{language === "ar" ? "الوصف" : "Description"}</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder={language === "ar" ? "وصف عملية الصيانة..." : "Describe the maintenance operation..."}
                rows={3}
              />
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="current_km">{language === "ar" ? "الكيلومترات الحالية" : "Current KM"}</Label>
                <Input
                  id="current_km"
                  type="number"
                  value={formData.current_km}
                  onChange={(e) => setFormData({ ...formData, current_km: e.target.value })}
                  placeholder="50000"
                  min="0"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="labor_cost_egp">{language === "ar" ? "تكلفة العمالة (ج.م)" : "Labor Cost (EGP)"}</Label>
                <Input
                  id="labor_cost_egp"
                  type="number"
                  step="0.01"
                  value={formData.labor_cost_egp}
                  onChange={(e) => setFormData({ ...formData, labor_cost_egp: e.target.value })}
                  placeholder="500.00"
                  min="0"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="parts_cost_egp">{language === "ar" ? "تكلفة القطع (ج.م)" : "Parts Cost (EGP)"}</Label>
                <Input
                  id="parts_cost_egp"
                  type="number"
                  step="0.01"
                  value={formData.parts_cost_egp}
                  onChange={(e) => setFormData({ ...formData, parts_cost_egp: e.target.value })}
                  placeholder="1500.00"
                  min="0"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="notes">{language === "ar" ? "ملاحظات" : "Notes"}</Label>
              <Textarea
                id="notes"
                value={formData.notes}
                onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                placeholder={language === "ar" ? "ملاحظات إضافية..." : "Additional notes..."}
                rows={2}
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setOpen(false)}>
              {language === "ar" ? "إلغاء" : "Cancel"}
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (language === "ar" ? "جاري الإضافة..." : "Adding...") : language === "ar" ? "إضافة" : "Add"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
