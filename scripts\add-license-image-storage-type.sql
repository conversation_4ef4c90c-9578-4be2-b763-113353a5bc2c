-- Add license_image_storage_type column to drivers table
-- This column will track whether the license image is stored as a URL or base64

ALTER TABLE public.drivers 
ADD COLUMN IF NOT EXISTS license_image_storage_type CHARACTER VARYING DEFAULT 'url';

-- Add a comment to explain the column purpose
COMMENT ON COLUMN public.drivers.license_image_storage_type IS 'نوع تخزين الصورة: ''url'' أو ''base64'' - Storage type for license image: ''url'' or ''base64''';

-- Update existing records to set the default value for records that have license_image_url
UPDATE public.drivers 
SET license_image_storage_type = 'url' 
WHERE license_image_url IS NOT NULL AND license_image_storage_type IS NULL;

-- For records that might have base64 data in license_image_url, we can't automatically detect them
-- Applications should handle this logic when retrieving the data