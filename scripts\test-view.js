const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: './.env.local' });

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials in .env.local');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testView() {
  console.log('Testing if upcoming_services view exists...');
  
  try {
    // Test with a simple query first
    console.log('Testing simple vehicles query...');
    const { data: testData, error: testError } = await supabase
      .from('vehicles')
      .select('id, plate_number')
      .limit(1);
    
    if (testError) {
      console.error('Simple vehicles query failed:', testError);
      return;
    } else {
      console.log('Simple vehicles query successful:', testData);
    }
    
    // Now test the upcoming_services view
    console.log('Testing upcoming_services view query...');
    const { data, error } = await supabase
      .from('upcoming_services')
      .select('vehicle_id, plate_number, service_type')
      .limit(3);
    
    if (error) {
      console.error('upcoming_services view query failed:', error);
      console.log('This is expected if you haven\'t created the view yet.');
      console.log('Please follow the instructions from generate-view-sql.js to create the view.');
    } else {
      console.log('upcoming_services view query successful!');
      console.log('Data:', data);
    }
  } catch (err) {
    console.error('Error:', err);
  }
}

testView();