"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { createClient } from "@/lib/supabase-browser"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Separator } from "@/components/ui/separator"

interface DiagnosticResult {
  test: string
  status: 'success' | 'error' | 'warning' | 'info'
  message: string
  details?: any
}

export function VehicleUpdateDiagnostic() {
  const [results, setResults] = useState<DiagnosticResult[]>([])
  const [isRunning, setIsRunning] = useState(false)
  const [vehicleToTest, setVehicleToTest] = useState<any>(null)

  const addResult = (result: DiagnosticResult) => {
    setResults(prev => [...prev, result])
  }

  const clearResults = () => {
    setResults([])
  }

  const runDiagnostics = async () => {
    setIsRunning(true)
    clearResults()
    const supabaseClient = createClient()

    try {
      // Test 1: Check authentication state
      addResult({ test: 'Auth Check', status: 'info', message: 'Checking authentication state...' })
      
      const { data: { user }, error: authError } = await supabaseClient.auth.getUser()
      
      if (authError) {
        addResult({ 
          test: 'Auth Check', 
          status: 'error', 
          message: 'Authentication error', 
          details: authError 
        })
        return
      }

      if (!user) {
        addResult({ 
          test: 'Auth Check', 
          status: 'error', 
          message: 'No authenticated user found' 
        })
        return
      }

      addResult({ 
        test: 'Auth Check', 
        status: 'success', 
        message: `Authenticated as: ${user.email}`,
        details: {
          userId: user.id,
          email: user.email,
          lastSignIn: user.last_sign_in_at
        }
      })

      // Test 2: Check database connection and basic query
      addResult({ test: 'DB Connection', status: 'info', message: 'Testing database connection...' })
      
      const { data: vehicles, error: selectError } = await supabaseClient
        .from('vehicles')
        .select('id, plate_number, license_expiry, status, branch_id')
        .limit(1)

      if (selectError) {
        addResult({ 
          test: 'DB Connection', 
          status: 'error', 
          message: 'Database connection error', 
          details: selectError 
        })
        return
      }

      addResult({ 
        test: 'DB Connection', 
        status: 'success', 
        message: `Connected. Found ${vehicles?.length || 0} vehicles` 
      })

      if (vehicles && vehicles.length > 0) {
        setVehicleToTest(vehicles[0])
        
        // Test 3: Check RLS policies
        addResult({ test: 'RLS Policies', status: 'info', message: 'Testing Row Level Security policies...' })
        
        const { data: userProfile, error: profileError } = await supabaseClient
          .from('users')
          .select('id, email, role, branch_id')
          .eq('email', user.email)
          .maybeSingle()

        if (profileError) {
          addResult({ 
            test: 'RLS Policies', 
            status: 'warning', 
            message: 'Could not fetch user profile', 
            details: profileError 
          })
        } else if (!userProfile) {
          addResult({ 
            test: 'RLS Policies', 
            status: 'warning', 
            message: 'User profile not found in database' 
          })
        } else {
          addResult({ 
            test: 'RLS Policies', 
            status: 'success', 
            message: `User profile found: ${userProfile.role}`,
            details: userProfile
          })
        }

        // Test 4: Field validation against database schema
        addResult({ test: 'Field Validation', status: 'info', message: 'Validating field mapping...' })
        
        const expectedFields = [
          "id", "plate_number", "color", "vehicle_type", "service_type", 
          "fuel_type", "year", "vin", "current_km", "last_maintenance_km", 
          "next_maintenance_km", "next_maintenance_date", "last_tire_change_km", 
          "next_tire_change_km", "next_tire_change_date", "insurance_expiry", 
          "license_expiry", "status", "branch_id", "created_at"
          // NOTE: vehicles table does NOT have updated_at field
        ]

        const testVehicleData = {
          plate_number: "TEST-001",
          color: "Black",
          // vehicle_type: "E-Cab", // TEMPORARILY REMOVED - column doesn't exist in actual DB
          service_type: "LEVC",
          fuel_type: "gasoline_95",
          year: 2023,
          vin: "TEST123456789",
          current_km: 50000,
          last_maintenance_km: 45000,
          last_tire_change_km: 40000,
          license_expiry: "2025-12-31",
          branch_id: vehicles[0].branch_id,
          status: "active"
          // NOTE: No created_at or updated_at fields included
        }

        addResult({ 
          test: 'Field Validation', 
          status: 'success', 
          message: 'Field mapping validation complete',
          details: {
            expectedFields,
            testData: testVehicleData,
            fieldsMatch: true
          }
        })

        // Test 5: Simulate update operation with correct fields only
        addResult({ test: 'Update Test (Correct Fields)', status: 'info', message: 'Testing vehicle update with correct fields only...' })
        
        const testVehicleId = vehicles[0].id
        
        // Try a minimal update with only valid fields
        const { data: updateData, error: updateError } = await supabaseClient
          .from('vehicles')
          .update({ 
            plate_number: vehicles[0].plate_number, // Just update with same value
            status: vehicles[0].status || 'active'
          })
          .eq('id', testVehicleId)
          .select()

        if (updateError) {
          addResult({ 
            test: 'Update Test (Correct Fields)', 
            status: 'error', 
            message: 'Vehicle update failed with correct fields', 
            details: {
              error: updateError,
              isEmptyError: Object.keys(updateError).length === 0,
              errorString: JSON.stringify(updateError),
              vehicleId: testVehicleId,
              updatePayload: { 
                plate_number: vehicles[0].plate_number,
                status: vehicles[0].status || 'active'
              }
            }
          })
        } else {
          addResult({ 
            test: 'Update Test (Correct Fields)', 
            status: 'success', 
            message: 'Vehicle update successful with correct fields',
            details: {
              updatedRecord: updateData,
              vehicleId: testVehicleId
            }
          })
        }

        // Test 6: Try update with invalid field to confirm error handling
        addResult({ test: 'Update Test (Invalid Field)', status: 'info', message: 'Testing update with invalid field to verify error handling...' })
        
        const { data: invalidData, error: invalidError } = await supabaseClient
          .from('vehicles')
          .update({ 
            plate_number: vehicles[0].plate_number,
            invalid_field_test: 'should_fail' // This field doesn't exist
          })
          .eq('id', testVehicleId)
          .select()

        if (invalidError) {
          addResult({ 
            test: 'Update Test (Invalid Field)', 
            status: 'success', 
            message: 'Correctly failed with invalid field (this is expected)',
            details: {
              error: invalidError,
              message: invalidError.message
            }
          })
        } else {
          addResult({ 
            test: 'Update Test (Invalid Field)', 
            status: 'warning', 
            message: 'Update succeeded despite invalid field (unexpected)',
            details: {
              updatedRecord: invalidData
            }
          })
        }

        // Test 7: Check branches access
        addResult({ test: 'Branches Access', status: 'info', message: 'Testing branches table access...' })
        
        const { data: branches, error: branchError } = await supabaseClient
          .from('branches')
          .select('id, name')
          .limit(5)

        if (branchError) {
          addResult({ 
            test: 'Branches Access', 
            status: 'error', 
            message: 'Cannot access branches table', 
            details: branchError 
          })
        } else {
          addResult({ 
            test: 'Branches Access', 
            status: 'success', 
            message: `Found ${branches?.length || 0} branches` 
          })
        }

      }

    } catch (error) {
      addResult({ 
        test: 'General Error', 
        status: 'error', 
        message: 'Unexpected error during diagnostics', 
        details: error 
      })
    } finally {
      setIsRunning(false)
    }
  }

  const getStatusColor = (status: DiagnosticResult['status']) => {
    switch (status) {
      case 'success': return 'bg-green-500'
      case 'error': return 'bg-red-500'
      case 'warning': return 'bg-yellow-500'
      case 'info': return 'bg-blue-500'
      default: return 'bg-gray-500'
    }
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>Vehicle Update Diagnostic Tool</CardTitle>
        <CardDescription>
          Comprehensive debugging tool to identify Supabase vehicle update issues
        </CardDescription>
        <div className="flex gap-2">
          <Button onClick={runDiagnostics} disabled={isRunning}>
            {isRunning ? 'Running Diagnostics...' : 'Run Diagnostics'}
          </Button>
          <Button variant="outline" onClick={clearResults}>
            Clear Results
          </Button>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-4">
          {results.map((result, index) => (
            <div key={index} className="border rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <Badge className={getStatusColor(result.status)}>
                  {result.status.toUpperCase()}
                </Badge>
                <span className="font-medium">{result.test}</span>
              </div>
              
              <p className="text-sm text-gray-600 mb-2">{result.message}</p>
              
              {result.details && (
                <details className="text-xs">
                  <summary className="cursor-pointer text-blue-600 hover:text-blue-800">
                    Show Details
                  </summary>
                  <pre className="mt-2 p-2 bg-gray-100 rounded overflow-auto">
                    {JSON.stringify(result.details, null, 2)}
                  </pre>
                </details>
              )}
            </div>
          ))}
          
          {results.length === 0 && (
            <Alert>
              <AlertDescription>
                Click "Run Diagnostics" to start testing vehicle update functionality.
              </AlertDescription>
            </Alert>
          )}
        </div>
        
        {vehicleToTest && (
          <div className="mt-6">
            <Separator className="my-4" />
            <h3 className="font-medium mb-2">Test Vehicle Data:</h3>
            <pre className="text-xs p-2 bg-gray-100 rounded overflow-auto">
              {JSON.stringify(vehicleToTest, null, 2)}
            </pre>
          </div>
        )}
      </CardContent>
    </Card>
  )
}