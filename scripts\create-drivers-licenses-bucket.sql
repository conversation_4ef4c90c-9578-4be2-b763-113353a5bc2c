-- Create the Drivers-licenses bucket if it doesn't exist
-- This script should be run in the Supabase SQL editor

INSERT INTO storage.buckets (id, name, public) 
VALUES ('Drivers-licenses', 'Drivers-licenses', true)
ON CONFLICT (id) DO UPDATE SET public = true;

-- Remove any existing policies on the Drivers-licenses bucket
DROP POLICY IF EXISTS "Allow read access to drivers licenses" ON storage.objects;
DROP POLICY IF EXISTS "Allow insert access to drivers licenses" ON storage.objects;
DROP POLICY IF EXISTS "Allow update access to drivers licenses" ON storage.objects;
DROP POLICY IF EXISTS "Allow delete access to drivers licenses" ON storage.objects;

-- Create new policies to allow any authenticated user to perform all operations
CREATE POLICY "Allow read access to drivers licenses"
ON storage.objects FOR SELECT
TO authenticated
USING ( bucket_id = 'Drivers-licenses' );

CREATE POLICY "Allow insert access to drivers licenses"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK ( bucket_id = 'Drivers-licenses' );

CREATE POLICY "Allow update access to drivers licenses"
ON storage.objects FOR UPDATE
TO authenticated
USING ( bucket_id = 'Drivers-licenses' );

CREATE POLICY "Allow delete access to drivers licenses"
ON storage.objects FOR DELETE
TO authenticated
USING ( bucket_id = 'Drivers-licenses' );

-- Grant necessary permissions to the storage.objects table
GRANT ALL ON storage.objects TO authenticated;

-- Verify the bucket was created
SELECT * FROM storage.buckets WHERE id = 'Drivers-licenses';