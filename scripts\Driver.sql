-- جدول السائقين
CREATE TABLE IF NOT EXISTS public.drivers (
    id UUID NOT NULL DEFAULT gen_random_uuid(),
    full_name CHARACTER VARYING NOT NULL,
    national_id CHARACTER VARYING UNIQUE,
    license_number CHARACTER VARYING NOT NULL UNIQUE,
    license_expiry DATE NOT NULL,
    phone CHARACTER VARYING NOT NULL,
    emergency_phone CHARACTER VARYING,
    tourism_permit BOOLEAN DEFAULT false,
    airport_permit BOOLEAN DEFAULT false,
    performance_score NUMERIC DEFAULT 5.0,
    branch_id UUID,
    license_image_url TEXT, -- رابط صورة رخصة القيادة
    license_image_storage_type CHARACTER VARYING DEFAULT 'url', -- نوع تخزين الصورة: 'url' أو 'base64'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(), -- تاريخ الإنشاء
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(), -- تاريخ آخر تحديث
    last_performance_update TIMESTAMP WITH TIME ZONE,
    CONSTRAINT drivers_pkey PRIMARY KEY (id),
    CONSTRAINT drivers_branch_id_fkey FOREIGN KEY (branch_id) REFERENCES public.branches(id)
);

-- جدول أنواع المخالفات
CREATE TABLE IF NOT EXISTS public.violation_types (
    id UUID NOT NULL DEFAULT gen_random_uuid(),
    name CHARACTER VARYING NOT NULL,
    description TEXT,
    severity_score NUMERIC NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT violation_types_pkey PRIMARY KEY (id)
);

-- جدول المخالفات والجزاءات
CREATE TABLE IF NOT EXISTS public.driver_violations (
    id UUID NOT NULL DEFAULT gen_random_uuid(),
    driver_id UUID NOT NULL,
    violation_type_id UUID NOT NULL,
    violation_date TIMESTAMP WITH TIME ZONE NOT NULL,
    description TEXT,
    penalty_amount NUMERIC DEFAULT 0,
    penalty_status CHARACTER VARYING DEFAULT 'pending',
    accident_severity CHARACTER VARYING,
    points_deducted NUMERIC DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT driver_violations_pkey PRIMARY KEY (id),
    CONSTRAINT driver_violations_driver_id_fkey FOREIGN KEY (driver_id) REFERENCES public.drivers(id),
    CONSTRAINT driver_violations_violation_type_id_fkey FOREIGN KEY (violation_type_id) REFERENCES public.violation_types(id)
);

-- جدول وثائق السائقين
CREATE TABLE IF NOT EXISTS public.driver_documents (
    id UUID NOT NULL DEFAULT gen_random_uuid(),
    driver_id UUID NOT NULL,
    document_type CHARACTER VARYING NOT NULL, -- 'license', 'national_id', 'insurance', etc.
    document_name CHARACTER VARYING NOT NULL,
    file_url TEXT NOT NULL,
    file_name CHARACTER VARYING NOT NULL,
    file_size BIGINT,
    mime_type CHARACTER VARYING,
    is_verified BOOLEAN DEFAULT false,
    verified_by UUID,
    verified_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT driver_documents_pkey PRIMARY KEY (id),
    CONSTRAINT driver_documents_driver_id_fkey FOREIGN KEY (driver_id) REFERENCES public.drivers(id),
    CONSTRAINT driver_documents_verified_by_fkey FOREIGN KEY (verified_by) REFERENCES auth.users(id)
);

-- دالة لتحديث تقييم السائق تلقائياً
CREATE OR REPLACE FUNCTION public.update_driver_performance_score()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE public.drivers 
    SET 
        performance_score = GREATEST(1.0, 
        5.0 - COALESCE((
            SELECT SUM(vt.severity_score * dv.points_deducted)
            FROM public.driver_violations dv
            JOIN public.violation_types vt ON dv.violation_type_id = vt.id
            WHERE dv.driver_id = NEW.driver_id
            AND dv.violation_date > NOW() - INTERVAL '365 days'
        ), 0)),
        last_performance_update = NOW()
    WHERE id = NEW.driver_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- دالة لإنشاء رابط موقّع للوصول الآمن إلى الملفات
CREATE OR REPLACE FUNCTION public.generate_signed_url(file_path TEXT, expires_in INTERVAL DEFAULT '1 hour')
RETURNS TEXT AS $$
DECLARE
    signed_url TEXT;
BEGIN
    SELECT storage.sign_url(file_path, expires_in) INTO signed_url;
    RETURN signed_url;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- إدخال بيانات أنواع المخالفات الأساسية
INSERT INTO public.violation_types (name, description, severity_score) VALUES
('تجاوز السرعة', 'تجاوز السرعة المحددة بأكثر من 20%', 0.5),
('عدم ارتداء الحزام', 'عدم ارتداء حزام الأمان', 0.3),
('استخدام الهاتف أثناء القيادة', 'استخدام الهاتف المحمول أثناء القيادة', 0.4),
('عدم إعطاء الأولوية', 'عدم إعطاء الأولوية للمشاة أو المركبات الأخرى', 0.6),
('الوقوف في مكان ممنوع', 'الوقوف في أماكن ممنوعة أو تعيق الحركة', 0.2),
('حادث بسيط', 'حادث مروري بسيط بدون إصابات', 1.0),
('حادث متوسط', 'حادث مروري متوسط الشدة بإصابات طفيفة', 2.0),
('حادث شديد', 'حادث مروري خطير بإصابات بليغة', 3.0),
('قيادة بدون رخصة', 'القيادة بدون رخصة سارية المفعول', 1.5),
('عدم الصيانة الدورية', 'عدم إجراء الصيانة الدورية للمركبة', 0.3)
ON CONFLICT (id) DO NOTHING;

-- دالة لتحديث عمود updated_at تلقائياً
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إنشاء trigger لتحديث عمود updated_at تلقائياً
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_trigger WHERE tgname = 'update_drivers_updated_at'
    ) THEN
        CREATE TRIGGER update_drivers_updated_at 
            BEFORE UPDATE ON public.drivers 
            FOR EACH ROW 
            EXECUTE FUNCTION public.update_updated_at_column();
    END IF;
END $$;

-- تعليق توضيحي لطريقة استخدام النظام:
/*
طريقة تخزين وعرض صور رخص القيادة:

1. رفع الملف إلى Supabase Storage:
   - المسار: drivers-licenses/{driver_id}/license-front.jpg
   - drivers-licenses/{driver_id}/license-back.jpg

2. حفظ رابط الملف في الجدول:
   - في حقل license_image_url (للوجه الأمامي)
   - أو في جدول driver_documents لكلا الوجهين

3. استرجاع الصورة بشكل آمن:
   - استخدام الدالة generate_signed_url() للحصول على رابط مؤقت
   - مثال: SELECT generate_signed_url('drivers-licenses/driver-uuid/license-front.jpg');

4. هيكل المجلدات في الـ bucket:
   drivers-licenses/
   ├── driver-uuid-1/
   │   ├── license-front.jpg
   │   └── license-back.jpg
   ├── driver-uuid-2/
   │   ├── license-front.jpg
   │   └── national-id.jpg
   └── driver-uuid-3/
       ├── license-front.jpg
       └── insurance.pdf
*/