# Database Setup Instructions

## Create the upcoming_services View

The application requires a database view called `upcoming_services` to function properly. If this view doesn't exist in your database, you'll see 404 errors in the console.

### Steps to Create the View:

1. Go to your Supabase project dashboard at https://app.supabase.com/project/tilywrugwehpbeljwmsx
2. Navigate to the SQL Editor in the left sidebar
3. Create a new query
4. Copy and paste the following SQL:

```sql
CREATE OR REPLACE VIEW public.upcoming_services AS
-- 1. الصيانة الدورية القادمة بناءً على الكيلومترات
SELECT 
    v.id AS vehicle_id,
    v.plate_number,
    'maintenance_km' AS service_type,
    'صيانة دورية' AS service_type_ar,
    v.next_maintenance_km AS trigger_value,
    v.current_km AS current_value,
    (v.next_maintenance_km - v.current_km) AS remaining_km,
    NULL::date AS due_date,
    NULL::integer AS reminder_days,
    v.branch_id
FROM 
    vehicles v
WHERE 
    v.next_maintenance_km IS NOT NULL 
    AND v.current_km IS NOT NULL
    AND (v.next_maintenance_km - v.current_km) < 1000 -- إظهار عندما تتبقى 1000 كم أو أقل

UNION ALL

-- 2. الصيانة الدورية القادمة بناءً على التاريخ
SELECT 
    v.id AS vehicle_id,
    v.plate_number,
    'maintenance_date' AS service_type,
    'صيانة دورية' AS service_type_ar,
    NULL::integer AS trigger_value,
    NULL::integer AS current_value,
    NULL::integer AS remaining_km,
    v.next_maintenance_date AS due_date,
    30 AS reminder_days, -- تنبيه قبل 30 يوم
    v.branch_id
FROM 
    vehicles v
WHERE 
    v.next_maintenance_date IS NOT NULL 
    AND v.next_maintenance_date <= (CURRENT_DATE + INTERVAL '30 days')

UNION ALL

-- 3. تغيير الإطارات القادم بناءً على الكيلومترات
SELECT 
    v.id AS vehicle_id,
    v.plate_number,
    'tire_change_km' AS service_type,
    'تغيير إطارات' AS service_type_ar,
    v.next_tire_change_km AS trigger_value,
    v.current_km AS current_value,
    (v.next_tire_change_km - v.current_km) AS remaining_km,
    NULL::date AS due_date,
    NULL::integer AS reminder_days,
    v.branch_id
FROM 
    vehicles v
WHERE 
    v.next_tire_change_km IS NOT NULL 
    AND v.current_km IS NOT NULL
    AND (v.next_tire_change_km - v.current_km) < 1000 -- إظهار عندما تتبقى 1000 كم أو أقل

UNION ALL

-- 4. تغيير الإطارات القادم بناءً على التاريخ
SELECT 
    v.id AS vehicle_id,
    v.plate_number,
    'tire_change_date' AS service_type,
    'تغيير إطارات' AS service_type_ar,
    NULL::integer AS trigger_value,
    NULL::integer AS current_value,
    NULL::integer AS remaining_km,
    v.next_tire_change_date AS due_date,
    30 AS reminder_days, -- تنبيه قبل 30 يوم
    v.branch_id
FROM 
    vehicles v
WHERE 
    v.next_tire_change_date IS NOT NULL 
    AND v.next_tire_change_date <= (CURRENT_DATE + INTERVAL '30 days')

UNION ALL

-- 5. انتهاء رخصة المركبة
SELECT 
    v.id AS vehicle_id,
    v.plate_number,
    'license_expiry' AS service_type,
    'انتهاء رخصة المركبة' AS service_type_ar,
    NULL::integer AS trigger_value,
    NULL::integer AS current_value,
    NULL::integer AS remaining_km,
    v.license_expiry AS due_date,
    30 AS reminder_days, -- تنبيه قبل 30 يوم
    v.branch_id
FROM 
    vehicles v
WHERE 
    v.license_expiry IS NOT NULL 
    AND v.license_expiry <= (CURRENT_DATE + INTERVAL '30 days')

UNION ALL

-- 6. انتهاء التأمين
SELECT 
    v.id AS vehicle_id,
    v.plate_number,
    'insurance_expiry' AS service_type,
    'انتهاء التأمين' AS service_type_ar,
    NULL::integer AS trigger_value,
    NULL::integer AS current_value,
    NULL::integer AS remaining_km,
    v.insurance_expiry AS due_date,
    30 AS reminder_days, -- تنبيه قبل 30 يوم
    v.branch_id
FROM 
    vehicles v
WHERE 
    v.insurance_expiry IS NOT NULL 
    AND v.insurance_expiry <= (CURRENT_DATE + INTERVAL '30 days')

UNION ALL

-- 7. انتهاء رخصة السائق (من جدول drivers)
SELECT 
    v.id AS vehicle_id,
    v.plate_number,
    'driver_license_expiry' AS service_type,
    'انتهاء رخصة السائق' AS service_type_ar,
    NULL::integer AS trigger_value,
    NULL::integer AS current_value,
    NULL::integer AS remaining_km,
    d.license_expiry AS due_date,
    30 AS reminder_days, -- تنبيه قبل 30 يوم
    v.branch_id
FROM 
    vehicles v
    JOIN vehicle_assignments va ON v.id = va.vehicle_id AND va.end_date IS NULL
    JOIN drivers d ON va.driver_id = d.id
WHERE 
    d.license_expiry IS NOT NULL 
    AND d.license_expiry <= (CURRENT_DATE + INTERVAL '30 days')

ORDER BY 
    due_date ASC NULLS LAST, 
    remaining_km ASC NULLS LAST;
```

5. Click "Run" to execute the query

## Setup Driver License Storage

The application requires a properly configured Supabase storage bucket for driver license images. Due to security restrictions, this must be set up manually.

### Steps to Setup Driver License Storage:

1. In your Supabase project dashboard, navigate to the SQL Editor
2. Create a new query
3. Copy and paste the following SQL to create the bucket:

```sql
-- Create the Drivers-licenses bucket
INSERT INTO storage.buckets (id, name, public) 
VALUES ('Drivers-licenses', 'Drivers-licenses', true)
ON CONFLICT (id) DO UPDATE SET public = true;
```

4. Click "Run" to execute the query
5. Run the storage policies script to configure access permissions:

```sql
-- Allow anyone to view public images (SELECT)
DROP POLICY IF EXISTS "Allow public read access to driver licenses" ON storage.objects;
CREATE POLICY "Allow public read access to driver licenses"
ON storage.objects FOR SELECT
USING ( bucket_id = 'Drivers-licenses' );

-- Allow ANY authenticated user to upload images (INSERT)
DROP POLICY IF EXISTS "Allow any authenticated user to upload driver licenses" ON storage.objects;
CREATE POLICY "Allow any authenticated user to upload driver licenses"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK ( bucket_id = 'Drivers-licenses' );

-- Allow ANY authenticated user to update driver licenses (UPDATE)
DROP POLICY IF EXISTS "Allow any authenticated user to update driver licenses" ON storage.objects;
CREATE POLICY "Allow any authenticated user to update driver licenses"
ON storage.objects FOR UPDATE
TO authenticated
USING ( bucket_id = 'Drivers-licenses' )
WITH CHECK ( bucket_id = 'Drivers-licenses' );

-- Allow ANY authenticated user to delete driver licenses (DELETE)
DROP POLICY IF EXISTS "Allow any authenticated user to delete driver licenses" ON storage.objects;
CREATE POLICY "Allow any authenticated user to delete driver licenses"
ON storage.objects FOR DELETE
TO authenticated
USING ( bucket_id = 'Drivers-licenses' );
```

## Verify the View was Created

After creating the view, you can verify it exists by running this test script:

```bash
cd fleet-management-dashboard
node scripts/test-view.js
```

If successful, you should see a message indicating that the `upcoming_services` view query was successful.

## Service Role Key

Make sure to add your Supabase service role key to the [.env.local](file:///E:/fleet-management-dashboard/.env.local) file:

```
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
```

You can find this key in your Supabase project dashboard under Project Settings > API.