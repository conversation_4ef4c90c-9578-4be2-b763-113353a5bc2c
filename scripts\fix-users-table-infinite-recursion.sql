-- Fix infinite recursion in users table RLS policies
-- This script identifies and fixes common RLS policy patterns that cause infinite recursion

-- First, let's check what policies exist on the users table
-- Run this query to see existing policies:
-- SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check 
-- FROM pg_policies WHERE tablename = 'users';

-- Common problematic patterns that cause infinite recursion:
-- 1. Policies that query the same table they're applied to
-- 2. Policies with subqueries like: (SELECT branch_id FROM users WHERE id = auth.uid())
-- 3. Circular references between tables

-- Drop potentially problematic policies (if they exist)
-- These are common patterns that might exist in your database

-- Drop any policy that might be causing recursion on users table
DROP POLICY IF EXISTS "Users can access their own data" ON users;
DROP POLICY IF EXISTS "Branch managers can access branch users" ON users;
DROP POLICY IF EXISTS "Admins can access all users" ON users;
DROP POLICY IF EXISTS "Users can view users in same branch" ON users;

-- Create safe, non-recursive policies for users table

-- 1. Allow users to access their own record
CREATE POLICY "Users can access own record" ON users
  FOR ALL TO authenticated
  USING (auth.uid() = id);

-- 2. For admin access, use a simple role-based check without subqueries
-- Note: This assumes admin role is stored in JWT claims or use service role for admin operations
CREATE POLICY "Service role can access all users" ON users
  FOR ALL TO service_role
  USING (true);

-- Alternative admin policy if role is in JWT (uncomment if needed):
-- CREATE POLICY "Admins can access all users" ON users
--   FOR ALL TO authenticated
--   USING (auth.jwt() ->> 'role' = 'admin');

-- 3. For branch-based access, avoid subqueries to users table
-- Instead, store branch_id in JWT claims or use a different approach
-- CREATE POLICY "Branch access via JWT" ON users
--   FOR SELECT TO authenticated
--   USING (branch_id = (auth.jwt() ->> 'branch_id')::uuid);

-- Enable RLS on users table (if not already enabled)
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Additional fixes for other tables that might reference users

-- Fix any policies on other tables that might cause recursion
-- Example: If branches table has policies referencing users
DROP POLICY IF EXISTS "Branch managers can access branches" ON branches;

-- Create a safe branch policy without recursion
CREATE POLICY "Users can access their branch" ON branches
  FOR SELECT TO authenticated
  USING (id = (auth.jwt() ->> 'branch_id')::uuid);

-- Fix any policies on vehicles table that might cause recursion
DROP POLICY IF EXISTS "Branch users can access branch vehicles" ON vehicles;

-- Create safe vehicle policies
CREATE POLICY "Users can access vehicles in their branch" ON vehicles
  FOR SELECT TO authenticated
  USING (branch_id = (auth.jwt() ->> 'branch_id')::uuid);

-- Fix any policies on drivers table that might cause recursion
DROP POLICY IF EXISTS "Branch users can access branch drivers" ON drivers;

-- Create safe driver policies
CREATE POLICY "Users can access drivers in their branch" ON drivers
  FOR SELECT TO authenticated
  USING (branch_id = (auth.jwt() ->> 'branch_id')::uuid);

-- Important Notes:
-- 1. The above policies assume branch_id is stored in JWT claims
-- 2. If you don't have branch_id in JWT, you'll need to modify the authentication flow
-- 3. For admin operations, use the service role key instead of complex RLS policies
-- 4. Test each policy individually to ensure they don't cause recursion

-- To add branch_id to JWT claims, modify your authentication function to include:
-- claims.branch_id = user_record.branch_id

-- Verification queries (run these to check if policies are working):
-- SELECT * FROM users LIMIT 1; -- Should work for authenticated users
-- SELECT * FROM branches LIMIT 1; -- Should work if branch_id is in JWT
-- SELECT * FROM vehicles LIMIT 1; -- Should work if branch_id is in JWT
