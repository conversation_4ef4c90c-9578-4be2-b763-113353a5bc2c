"use client"

import type React from "react"

import { useState, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Upload, Plus, X } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { createClient, resetClient } from "@/lib/supabase-browser"
import { uploadDriverLicenseImage } from "@/lib/driver-license-service"

interface AddDriverDialogProps {
  language: "ar" | "en"
  onDriverAdded?: () => void
}

export function AddDriverDialog({ language, onDriverAdded }: AddDriverDialogProps) {
  const [open, setOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const { toast } = useToast()
  const licenseImageRef = useRef<HTMLInputElement>(null)

  const [formData, setFormData] = useState({
    full_name: "",
    national_id: "",
    license_number: "",
    license_expiry: "",
    phone: "",
    emergency_phone: "",
    // Note: tourism_permit and airport_permit are optional fields
    tourism_permit: false,
    airport_permit: false,
  })

  const [licenseImage, setLicenseImage] = useState<File | null>(null)
  const [licenseImagePreview, setLicenseImagePreview] = useState<string | null>(null)

  // Handle license image selection
  const handleLicenseImageChange = (file: File | null) => {
    if (file) {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
      if (!allowedTypes.includes(file.type)) {
        toast({
          title: language === "ar" ? "نوع ملف غير مدعوم" : "Unsupported file type",
          description: language === "ar" ? "يرجى استخدام JPEG, PNG أو WebP" : "Please use JPEG, PNG or WebP",
          variant: "destructive"
        })
        return
      }
      
      // Validate file size (5MB max)
      if (file.size > 5 * 1024 * 1024) {
        toast({
          title: language === "ar" ? "حجم الملف كبير" : "File too large",
          description: language === "ar" ? "الحد الأقصى 5 ميجابايت" : "Maximum size is 5MB",
          variant: "destructive"
        })
        return
      }
      
      // Update license image state
      setLicenseImage(file)
      
      // Create preview URL
      const previewUrl = URL.createObjectURL(file)
      setLicenseImagePreview(previewUrl)
    } else {
      // Remove image
      setLicenseImage(null)
      setLicenseImagePreview(null)
      
      // Reset file input
      if (licenseImageRef.current) {
        licenseImageRef.current.value = ''
      }
    }
  }
  
  // Remove license image
  const removeLicenseImage = () => {
    handleLicenseImageChange(null)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      // Basic validation
      if (!formData.full_name.trim()) {
        throw new Error(language === "ar" ? "الاسم الكامل مطلوب" : "Full name is required")
      }

      if (!formData.license_number.trim()) {
        throw new Error(language === "ar" ? "رقم الرخصة مطلوب" : "License number is required")
      }

      if (!formData.license_expiry.trim()) {
        throw new Error(language === "ar" ? "تاريخ انتهاء الرخصة مطلوب" : "License expiry date is required")
      }

      if (!formData.phone.trim()) {
        throw new Error(language === "ar" ? "رقم الهاتف مطلوب" : "Phone number is required")
      }

      // Prepare data for insertion - ONLY include fields that have values
      // This avoids schema cache issues with optional columns
      const driverData: Record<string, any> = {
        full_name: formData.full_name.trim(),
        license_number: formData.license_number.trim(),
        license_expiry: formData.license_expiry.trim(),
        phone: formData.phone.trim(),
        created_at: new Date().toISOString(),
      }

      // Add optional fields only if they have values
      if (formData.national_id?.trim()) {
        driverData.national_id = formData.national_id.trim()
      }
      
      if (formData.emergency_phone?.trim()) {
        driverData.emergency_phone = formData.emergency_phone.trim()
      }
      
      // Only add permit fields if they are explicitly set to true
      // This avoids issues with schema cache not recognizing the columns
      if (formData.tourism_permit === true) {
        driverData.tourism_permit = true
      }
      
      if (formData.airport_permit === true) {
        driverData.airport_permit = true
      }

      // Try to insert into Supabase
      const supabase = createClient()
      let { data, error } = await supabase.from("drivers").insert([driverData]).select(
        "id, full_name, national_id, license_number, license_expiry, phone, emergency_phone, tourism_permit, airport_permit, performance_score, branch_id, license_image_url, license_image_storage_type, created_at, last_performance_update"
      )

      if (error) {
        console.warn("Supabase insert failed:", error)
        // If we get a schema cache error, try to reset the client and retry
        if (error.message.includes('schema cache') || error.message.includes('column') && error.message.includes('drivers')) {
          console.warn('Schema cache error detected during insert, resetting client and retrying...')
          // Reset the Supabase client to clear the schema cache
          resetClient()
          
          // Create a small delay to ensure the reset is processed
          await new Promise(resolve => setTimeout(resolve, 100))
          
          // Retry the insert
          const newSupabase = createClient()
          const { data: retryData, error: retryError } = await newSupabase.from("drivers").insert([driverData]).select()
          
          if (retryError) {
            throw new Error(`Insert error after client reset: ${retryError.message}`)
          }
          
          data = retryData
        } else {
          throw new Error(`Insert error: ${error.message}`)
        }
      }

      // Handle license image upload if provided
      if (licenseImage && data && data[0]) {
        console.log('📸 Starting license image upload for new driver...')
        
        const uploadResult = await uploadDriverLicenseImage(licenseImage, data[0].id)
        
        if (!uploadResult.success) {
          console.warn('⚠️ License image failed to upload:', uploadResult.error)
          
          toast({
            title: language === "ar" ? "تم إضافة السائق مع تحذير" : "Driver Added with Warning",
            description: 
              language === "ar"
                ? `تم إضافة السائق ${formData.full_name} لكن فشل رفع صورة الرخصة: ${uploadResult.error}`
                : `Driver ${formData.full_name} added but license image failed to upload: ${uploadResult.error}`,
            variant: "default"
          })
        } else {
          console.log('✅ License image uploaded successfully')
        }
      }

      // Show success message
      toast({
        title: language === "ar" ? "تم إضافة السائق" : "Driver Added",
        description:
          language === "ar"
            ? `تم إضافة السائق ${formData.full_name} بنجاح`
            : `Driver ${formData.full_name} has been added successfully`,
      })

      // Reset form and close dialog
      setFormData({
        full_name: "",
        national_id: "",
        license_number: "",
        license_expiry: "",
        phone: "",
        emergency_phone: "",
        tourism_permit: false,
        airport_permit: false,
      })
      
      // Reset license image
      setLicenseImage(null)
      setLicenseImagePreview(null)
      if (licenseImageRef.current) {
        licenseImageRef.current.value = ''
      }
      
      setOpen(false)

      // Trigger refresh if callback provided
      if (onDriverAdded) {
        onDriverAdded()
      }
    } catch (err) {
      console.error("Error adding driver:", err)
      toast({
        title: language === "ar" ? "خطأ" : "Error",
        description: err instanceof Error ? err.message : (language === "ar" ? "حدث خطأ أثناء إضافة السائق" : "An error occurred while adding the driver"),
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          {language === "ar" ? "إضافة سائق" : "Add Driver"}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>{language === "ar" ? "إضافة سائق جديد" : "Add New Driver"}</DialogTitle>
          <DialogDescription>
            {language === "ar" ? "أدخل تفاصيل السائق الجديد" : "Enter the details of the new driver"}
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="flex-1 overflow-y-auto">
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="full_name">{language === "ar" ? "الاسم الكامل" : "Full Name"} *</Label>
              <Input
                id="full_name"
                value={formData.full_name}
                onChange={(e) => setFormData({ ...formData, full_name: e.target.value })}
                placeholder={language === "ar" ? "أحمد محمد علي" : "Ahmed Mohamed Ali"}
                required
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="national_id">{language === "ar" ? "رقم الهوية" : "National ID"}</Label>
                <Input
                  id="national_id"
                  value={formData.national_id}
                  onChange={(e) => setFormData({ ...formData, national_id: e.target.value })}
                  placeholder="12345678901234"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="license_number">{language === "ar" ? "رقم الرخصة" : "License Number"} *</Label>
                <Input
                  id="license_number"
                  value={formData.license_number}
                  onChange={(e) => setFormData({ ...formData, license_number: e.target.value })}
                  placeholder="DL123456"
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="phone">{language === "ar" ? "رقم الهاتف" : "Phone Number"} *</Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                  placeholder="+966501234567"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="emergency_phone">{language === "ar" ? "رقم الطوارئ" : "Emergency Phone"}</Label>
                <Input
                  id="emergency_phone"
                  value={formData.emergency_phone}
                  onChange={(e) => setFormData({ ...formData, emergency_phone: e.target.value })}
                  placeholder="+966501234567"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="license_expiry">{language === "ar" ? "انتهاء الرخصة" : "License Expiry"} *</Label>
                <Input
                  id="license_expiry"
                  type="date"
                  value={formData.license_expiry}
                  onChange={(e) => setFormData({ ...formData, license_expiry: e.target.value })}
                  required
                />
              </div>
            </div>

            {/* Permits */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="tourism_permit"
                  checked={formData.tourism_permit}
                  onCheckedChange={(checked) => setFormData({ ...formData, tourism_permit: checked })}
                />
                <Label htmlFor="tourism_permit">{language === "ar" ? "تصريح سياحي" : "Tourism Permit"}</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Switch
                  id="airport_permit"
                  checked={formData.airport_permit}
                  onCheckedChange={(checked) => setFormData({ ...formData, airport_permit: checked })}
                />
                <Label htmlFor="airport_permit">{language === "ar" ? "تصريح مطار" : "Airport Permit"}</Label>
              </div>
            </div>

            {/* License Image Section */}
            <div className="space-y-4">
              <div className="border-t pt-4">
                <h4 className="font-medium mb-4">{language === "ar" ? "صورة رخصة القيادة" : "Driver's License Image"}</h4>
                
                <div className="grid grid-cols-1 gap-6">
                  {/* License Image */}
                  <div className="space-y-3">
                    <Label>{language === "ar" ? "صورة رخصة القيادة" : "Driver's License"}</Label>
                    
                    {/* New Image Preview */}
                    {licenseImagePreview && (
                      <div className="relative">
                        <img 
                          src={licenseImagePreview} 
                          alt={language === "ar" ? "معاينة صورة رخصة القيادة" : "Driver's license preview"}
                          className="w-full h-40 object-contain rounded-lg border"
                        />
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          className="absolute top-2 right-2"
                          onClick={removeLicenseImage}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                        <p className="text-xs text-green-600 mt-1">
                          {language === "ar" ? "صورة جديدة" : "New image"}
                        </p>
                      </div>
                    )}
                    
                    {/* Upload Interface */}
                    {!licenseImagePreview && (
                      <div 
                        className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center cursor-pointer hover:border-gray-400 transition-colors"
                        onClick={() => licenseImageRef.current?.click()}
                      >
                        <Upload className="mx-auto h-8 w-8 text-gray-400 mb-2" />
                        <p className="text-sm text-gray-600">
                          {language === "ar" ? "انقر لرفع صورة جديدة" : "Click to upload new image"}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          {language === "ar" ? "JPEG, PNG, WebP (حد أقصى 5MB)" : "JPEG, PNG, WebP (max 5MB)"}
                        </p>
                      </div>
                    )}
                    
                    <input
                      ref={licenseImageRef}
                      type="file"
                      accept="image/jpeg,image/jpg,image/png,image/webp"
                      className="hidden"
                      onChange={(e) => {
                        const file = e.target.files?.[0] || null
                        handleLicenseImageChange(file)
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setOpen(false)}>
              {language === "ar" ? "إلغاء" : "Cancel"}
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (language === "ar" ? "جاري الإضافة..." : "Adding...") : language === "ar" ? "إضافة" : "Add"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}